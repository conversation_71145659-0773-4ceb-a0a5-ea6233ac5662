# جدولة الامتحان - المصدر الوحيد للبيانات

## نظرة عامة

تم تحديث النظام ليجعل جدول `جدولة_الامتحان` في قاعدة البيانات هو **المصدر الوحيد** لحفظ وعرض جميع بيانات جدولة الامتحانات.

## الهيكل الجديد

### جدول `جدولة_الامتحان` - المصدر الوحيد

```sql
CREATE TABLE جدولة_الامتحان (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    اليوم TEXT,
    التاريخ TEXT,
    الحصة1 TEXT,
    التوقيت1 TEXT,
    الحصة2 TEXT,
    التوقيت2 TEXT,
    الحصة3 TEXT,
    التوقيت3 TEXT,
    الحصة4 TEXT,
    التوقيت4 TEXT,
    السنة_الدراسية TEXT,
    الأسدس TEXT,
    تاريخ_التحديث TEXT,
    ملاحظات TEXT
)
```

## الملفات المحدثة

### 1. `sub26_window.py` - النافذة الرئيسية
- **الوظيفة**: النافذة الرئيسية لإدارة جدولة الامتحانات
- **التحديثات**:
  - إضافة تعليقات توضيحية تؤكد أن جدول `جدولة_الامتحان` هو المصدر الوحيد
  - تحسين دالة `loadScheduleFromDB()` لضمان التحميل من المصدر الوحيد
  - تحسين دالة `saveSchedule()` لضمان الحفظ في المصدر الوحيد
  - إضافة دالة `getScheduleFromDB()` للاستخدام الداخلي
  - إضافة دالة `getScheduleFromDBStatic()` للاستخدام من الملفات الأخرى

### 2. `sub40_window.py` - نافذة الغياب
- **التحديث**: تم تعديل الكود ليستخرج قائمة المواد من جدول `جدولة_الامتحان` بدلاً من الاعتماد على جدول `مواد_الامتحان` مباشرة

### 3. `print12.py` و `print14.py` - ملفات الطباعة
- **الحالة**: هذه الملفات تستخدم بالفعل جدول `جدولة_الامتحان` كمصدر للبيانات ✅

## كيفية الاستخدام

### للمطورين - الحصول على بيانات جدولة الامتحان

```python
# الطريقة الأولى: استخدام الدالة الثابتة
from sub26_window import ExamScheduleProfessional

schedule_data = ExamScheduleProfessional.getScheduleFromDBStatic("data.db")

# الطريقة الثانية: إنشاء كائن من الكلاس
exam_window = ExamScheduleProfessional(db_path="data.db")
schedule_data = exam_window.getScheduleFromDB()
```

### هيكل البيانات المُرجعة

```python
# كل سجل يحتوي على:
(
    اليوم,           # مثل: "الاثنين"
    التاريخ,         # مثل: "2024-06-10"
    الحصة1,          # اسم المادة
    التوقيت1,        # مثل: "08.00-10.00"
    الحصة2,          # اسم المادة
    التوقيت2,        # مثل: "10.30-12.30"
    الحصة3,          # اسم المادة
    التوقيت3,        # مثل: "14.30-16.30"
    الحصة4,          # اسم المادة
    التوقيت4,        # مثل: "17.00-19.00"
    السنة_الدراسية,  # مثل: "2023-2024"
    الأسدس,          # مثل: "الأول"
    تاريخ_التحديث,   # مثل: "2024-05-18 12:30:00"
    ملاحظات          # نص إضافي
)
```

## المزايا

1. **مصدر واحد موثوق**: جميع البيانات تأتي من مكان واحد
2. **تجنب التضارب**: لا يوجد تضارب بين مصادر مختلفة للبيانات
3. **سهولة الصيانة**: تحديث واحد يؤثر على جميع أجزاء النظام
4. **الاتساق**: ضمان اتساق البيانات عبر جميع الوظائف
5. **التتبع**: إمكانية تتبع جميع التغييرات من مكان واحد

## ملاحظات مهمة

- ⚠️ **لا تستخدم** أي مصادر أخرى لبيانات جدولة الامتحانات
- ⚠️ **لا تعتمد** على ملفات JSON أو ملفات نصية لحفظ الجدولة
- ✅ **استخدم دائماً** جدول `جدولة_الامتحان` كمصدر وحيد
- ✅ **استخدم الدوال المتوفرة** في `sub26_window.py` للوصول للبيانات

## التحقق من التحديث

للتأكد من أن النظام يعمل بالشكل الصحيح:

1. افتح نافذة جدولة الامتحان (`sub26_window.py`)
2. أدخل بعض البيانات واحفظها
3. تحقق من أن البيانات محفوظة في جدول `جدولة_الامتحان`
4. أعد فتح النافذة وتأكد من تحميل البيانات بشكل صحيح
5. جرب الطباعة والتأكد من أن البيانات المطبوعة تطابق المحفوظة

---

**تاريخ التحديث**: 2024-05-18  
**المطور**: نظام إدارة الامتحانات
