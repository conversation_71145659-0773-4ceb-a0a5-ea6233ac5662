#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إزالة الإطار وتحسين موقع صورة التوجيهات
===============================================
"""

import os
import sys
from datetime import datetime

def test_image_improvements():
    """اختبار التحسينات على صورة التوجيهات"""
    
    print("🔍 اختبار التحسينات على صورة التوجيهات...")
    print("=" * 60)
    
    try:
        # استيراد ملف print12
        import print12
        print("✅ تم استيراد ملف print12.py بنجاح")
        
        # التحقق من وجود قاعدة البيانات
        db_path = "data.db"
        if not os.path.exists(db_path):
            print("❌ ملف قاعدة البيانات غير موجود!")
            return False
        
        print("✅ ملف قاعدة البيانات موجود")
        
        # فحص مسار الصورة
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        app_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
        images_folder = os.path.join(app_folder, "صور التوجيهات")
        image_path = os.path.join(images_folder, "توجيهات_المترشح.png")
        
        print(f"📁 مسار الصورة: {image_path}")
        
        if os.path.exists(image_path):
            print("✅ صورة التوجيهات موجودة")
            file_size = os.path.getsize(image_path)
            print(f"📊 حجم الصورة: {file_size} بايت")
        else:
            print("ℹ️ صورة التوجيهات غير موجودة")
            print("💡 يمكنك إنشاؤها من خلال نافذة التوجيهات في sub40_window.py")
        
        # إنشاء تقرير اختبار
        print("\n🧪 إنشاء تقرير اختبار للتحقق من التحسينات...")
        
        test_reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'اختبار_صورة_بدون_اطار')
        os.makedirs(test_reports_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        success, output_path, message = print12.print_exams_report(
            parent=None,
            report_title="اختبار صورة التوجيهات بدون إطار",
            output_dir=test_reports_dir,
            two_per_page=False
        )
        
        if success:
            print(f"✅ تم إنشاء التقرير بنجاح!")
            print(f"📁 مسار الملف: {output_path}")
            
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"📊 حجم الملف: {file_size} بايت")
                
                print("\n🎯 التحقق المطلوب:")
                print("1. افتح الملف المُنشأ")
                print("2. ابحث عن قسم 'توجيهات عامة للمترشح'")
                print("3. تأكد من عدم وجود إطار أزرق حول الصورة")
                print("4. تأكد من أن الصورة تملأ العرض مع هوامش 0.9 نقطة")
                print("5. تأكد من أن الصورة تبدأ من اليسار وتنتهي عند اليمين")
                
                print("\n✨ التحسينات المطبقة:")
                print("   ❌ إزالة الإطار الأزرق")
                print("   📏 هامش 0.9 نقطة من اليمين")
                print("   📏 هامش 0.9 نقطة من اليسار")
                print("   📐 الصورة تملأ العرض الكامل")
                
                return True
            else:
                print("❌ لم يتم إنشاء الملف")
                return False
        else:
            print(f"❌ فشل في إنشاء التقرير: {message}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def show_improvements_summary():
    """عرض ملخص التحسينات"""
    
    print("\n" + "=" * 60)
    print("📋 ملخص التحسينات المطبقة:")
    print("=" * 60)
    
    print("\n🔧 التغييرات المنجزة:")
    print("   ❌ إزالة الإطار الأزرق حول الصورة")
    print("   📏 تعديل الهوامش: 0.9 نقطة من كل جانب")
    print("   📐 تكبير عرض الصورة لتملأ المساحة المتاحة")
    print("   🎯 تحسين موضع الصورة")
    
    print("\n📊 المقاييس الجديدة:")
    print("   • عرض الصورة = عرض الصفحة - 1.8 نقطة (0.9 + 0.9)")
    print("   • موضع X = 0.9 نقطة من اليسار")
    print("   • ارتفاع الصورة = 80 ملليمتر (ثابت)")
    
    print("\n🎨 النتيجة البصرية:")
    print("   ✅ صورة نظيفة بدون إطار")
    print("   ✅ استغلال أمثل للمساحة")
    print("   ✅ هوامش متوازنة ومتسقة")
    print("   ✅ مظهر احترافي ونظيف")
    
    print("\n🔍 كيفية التحقق:")
    print("   1. أنشئ استدعاء من خلال sub40_window.py")
    print("   2. تأكد من وجود صورة التوجيهات")
    print("   3. افتح الملف وتحقق من المظهر الجديد")
    print("   4. قارن مع النسخة السابقة (إن وجدت)")

def create_test_image_if_missing():
    """إنشاء صورة اختبار إذا لم تكن موجودة"""
    
    print("\n📝 فحص وجود صورة التوجيهات...")
    print("-" * 50)
    
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    app_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
    images_folder = os.path.join(app_folder, "صور التوجيهات")
    image_path = os.path.join(images_folder, "توجيهات_المترشح.png")
    
    if os.path.exists(image_path):
        print("✅ صورة التوجيهات موجودة")
        return True
    else:
        print("❌ صورة التوجيهات غير موجودة")
        print("\n💡 لإنشاء صورة التوجيهات:")
        print("   1. افتح sub40_window.py")
        print("   2. اضغط على 'توجيهات عامة للمترشح (ة)'")
        print("   3. اختر إحدى الطريقتين:")
        print("      • 'اختيار صورة من الجهاز' (الزر الأزرق)")
        print("      • 'حفظ النص وإنشاء صورة' (الزر الأخضر)")
        print("   4. ثم شغّل هذا الاختبار مرة أخرى")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار التحسينات على صورة التوجيهات")
    print("=" * 60)
    
    # فحص وجود الصورة
    image_exists = create_test_image_if_missing()
    
    if image_exists:
        # تشغيل الاختبار
        test_result = test_image_improvements()
        
        # عرض ملخص التحسينات
        show_improvements_summary()
        
        print("\n" + "=" * 60)
        print("📋 نتيجة الاختبار:")
        
        if test_result:
            print("🎉 نجح الاختبار!")
            print("✨ التحسينات مطبقة بنجاح:")
            print("   ❌ تم إزالة الإطار الأزرق")
            print("   📏 تم تطبيق الهوامش الجديدة (0.9 نقطة)")
            print("   📐 تم تكبير عرض الصورة")
            print("📖 افتح الملف المُنشأ للتحقق البصري")
        else:
            print("⚠️ فشل الاختبار")
            print("🔧 تحقق من وجود البيانات والصورة")
    else:
        print("\n⚠️ لا يمكن إجراء الاختبار بدون صورة التوجيهات")
        print("📝 اتبع التعليمات أعلاه لإنشاء الصورة أولاً")
    
    print("=" * 60)
