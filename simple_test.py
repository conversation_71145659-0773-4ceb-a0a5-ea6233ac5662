#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def simple_test():
    """اختبار بسيط للتحقق من جدول جدولة_الامتحان"""
    
    print("🔍 اختبار بسيط للمصدر الوحيد...")
    
    db_path = "data.db"
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود!")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود جدول جدولة_الامتحان
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدولة_الامتحان'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ جدول جدولة_الامتحان موجود")
            
            # عد السجلات
            cursor.execute("SELECT COUNT(*) FROM جدولة_الامتحان")
            count = cursor.fetchone()[0]
            print(f"📊 عدد السجلات: {count}")
            
            # عرض هيكل الجدول
            cursor.execute("PRAGMA table_info(جدولة_الامتحان)")
            columns = cursor.fetchall()
            print("📋 أعمدة الجدول:")
            for col in columns:
                print(f"   - {col[1]} ({col[2]})")
                
        else:
            print("❌ جدول جدولة_الامتحان غير موجود!")
        
        conn.close()
        print("✅ الاختبار اكتمل بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    simple_test()
