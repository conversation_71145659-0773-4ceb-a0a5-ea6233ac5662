#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار المصدر الوحيد لجدولة الامتحان
=====================================

هذا الملف يختبر أن جدول 'جدولة_الامتحان' هو المصدر الوحيد 
لجميع بيانات جدولة الامتحانات في النظام.
"""

import sqlite3
import os
import sys
from datetime import datetime

def test_single_source_functionality():
    """اختبار وظائف المصدر الوحيد"""
    
    print("🔍 بدء اختبار المصدر الوحيد لجدولة الامتحان...")
    print("=" * 60)
    
    # مسار قاعدة البيانات
    db_path = "data.db"
    
    if not os.path.exists(db_path):
        print("❌ خطأ: ملف قاعدة البيانات غير موجود!")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. التحقق من وجود جدول جدولة_الامتحان
        print("1️⃣ التحقق من وجود جدول جدولة_الامتحان...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدولة_الامتحان'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("   ✅ جدول جدولة_الامتحان موجود")
        else:
            print("   ❌ جدول جدولة_الامتحان غير موجود!")
            return False
        
        # 2. التحقق من هيكل الجدول
        print("2️⃣ التحقق من هيكل جدول جدولة_الامتحان...")
        cursor.execute("PRAGMA table_info(جدولة_الامتحان)")
        columns = cursor.fetchall()
        
        required_columns = [
            'id', 'اليوم', 'التاريخ', 'الحصة1', 'التوقيت1',
            'الحصة2', 'التوقيت2', 'الحصة3', 'التوقيت3',
            'الحصة4', 'التوقيت4', 'السنة_الدراسية', 'الأسدس',
            'تاريخ_التحديث', 'ملاحظات'
        ]
        
        existing_columns = [col[1] for col in columns]
        missing_columns = [col for col in required_columns if col not in existing_columns]
        
        if missing_columns:
            print(f"   ❌ أعمدة مفقودة: {missing_columns}")
            return False
        else:
            print("   ✅ جميع الأعمدة المطلوبة موجودة")
        
        # 3. التحقق من وجود بيانات
        print("3️⃣ التحقق من وجود بيانات في الجدول...")
        cursor.execute("SELECT COUNT(*) FROM جدولة_الامتحان")
        count = cursor.fetchone()[0]
        print(f"   📊 عدد السجلات الموجودة: {count}")
        
        # 4. اختبار استخراج البيانات
        print("4️⃣ اختبار استخراج البيانات...")
        
        # الحصول على السنة الدراسية والأسدس
        cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
        result = cursor.fetchone()
        
        if result:
            academic_year, semester = result[0], result[1]
            print(f"   📅 السنة الدراسية: {academic_year}")
            print(f"   📚 الأسدس: {semester}")
            
            # استعلام عن البيانات
            cursor.execute("""
                SELECT اليوم, التاريخ,
                       الحصة1, التوقيت1,
                       الحصة2, التوقيت2,
                       الحصة3, التوقيت3,
                       الحصة4, التوقيت4
                FROM جدولة_الامتحان
                WHERE السنة_الدراسية = ? AND الأسدس = ?
                ORDER BY id
            """, (academic_year, semester))
            
            schedule_data = cursor.fetchall()
            print(f"   📋 عدد أيام الامتحان المجدولة: {len(schedule_data)}")
            
            # عرض تفاصيل البيانات
            if schedule_data:
                print("   📝 تفاصيل الجدولة:")
                for i, row in enumerate(schedule_data, 1):
                    day, date = row[0], row[1]
                    print(f"      {i}. {day} - {date}")
                    
                    # عد المواد في هذا اليوم
                    subjects_count = 0
                    for j in range(4):
                        subject = row[2 + j*2]
                        if subject:
                            subjects_count += 1
                    
                    print(f"         📚 عدد المواد: {subjects_count}")
        else:
            print("   ⚠️ لم يتم العثور على بيانات المؤسسة")
        
        # 5. اختبار الدوال الجديدة
        print("5️⃣ اختبار الدوال الجديدة...")
        
        try:
            # استيراد الكلاس
            sys.path.append('.')
            from sub26_window import ExamScheduleProfessional
            
            # اختبار الدالة الثابتة
            static_data = ExamScheduleProfessional.getScheduleFromDBStatic(db_path)
            print(f"   🔧 الدالة الثابتة: تم استرجاع {len(static_data)} سجل")
            
            # اختبار الدالة العادية
            exam_window = ExamScheduleProfessional(db_path=db_path)
            instance_data = exam_window.getScheduleFromDB()
            print(f"   🔧 دالة الكائن: تم استرجاع {len(instance_data)} سجل")
            
            # التحقق من تطابق البيانات
            if len(static_data) == len(instance_data):
                print("   ✅ البيانات متطابقة بين الدالتين")
            else:
                print("   ⚠️ عدم تطابق في البيانات بين الدالتين")
                
        except ImportError as e:
            print(f"   ❌ خطأ في استيراد الكلاس: {e}")
        except Exception as e:
            print(f"   ❌ خطأ في اختبار الدوال: {e}")
        
        conn.close()
        
        print("=" * 60)
        print("✅ اكتمل اختبار المصدر الوحيد بنجاح!")
        print("🎯 جدول 'جدولة_الامتحان' يعمل كمصدر وحيد للبيانات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_data_consistency():
    """اختبار اتساق البيانات"""
    
    print("\n🔄 اختبار اتساق البيانات...")
    print("-" * 40)
    
    db_path = "data.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من أن جدول مواد_الامتحان يتم تحديثه من جدول جدولة_الامتحان
        cursor.execute("SELECT COUNT(*) FROM مواد_الامتحان")
        subjects_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM جدولة_الامتحان")
        schedule_count = cursor.fetchone()[0]
        
        print(f"📊 عدد المواد في جدول مواد_الامتحان: {subjects_count}")
        print(f"📊 عدد السجلات في جدول جدولة_الامتحان: {schedule_count}")
        
        if subjects_count > 0 and schedule_count > 0:
            print("✅ البيانات متسقة بين الجدولين")
        elif schedule_count == 0:
            print("⚠️ لا توجد بيانات في جدول جدولة_الامتحان")
        else:
            print("ℹ️ جدول مواد_الامتحان فارغ - سيتم تحديثه عند الحفظ")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاتساق: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبارات المصدر الوحيد لجدولة الامتحان")
    print("=" * 80)
    
    # تشغيل الاختبارات
    test1_result = test_single_source_functionality()
    test2_result = test_data_consistency()
    
    print("\n" + "=" * 80)
    print("📋 ملخص النتائج:")
    print(f"   🔍 اختبار الوظائف الأساسية: {'✅ نجح' if test1_result else '❌ فشل'}")
    print(f"   🔄 اختبار اتساق البيانات: {'✅ نجح' if test2_result else '❌ فشل'}")
    
    if test1_result and test2_result:
        print("\n🎉 جميع الاختبارات نجحت! النظام يعمل بشكل صحيح.")
        print("✨ جدول 'جدولة_الامتحان' هو المصدر الوحيد للبيانات.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    print("=" * 80)
