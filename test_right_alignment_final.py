#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نهائي لمحاذاة التوجيهات العامة للمترشحين
==============================================

هذا الملف يختبر أن التوجيهات العامة للمترشحين تظهر بمحاذاة صحيحة إلى اليمين
بعد إعطاء الأولوية للنص بدلاً من الصورة.
"""

import os
import sys
import sqlite3
from datetime import datetime

def create_test_data():
    """إنشاء بيانات اختبار في جدولة_الامتحان"""
    
    print("📝 إنشاء بيانات اختبار...")
    
    try:
        db_path = "data.db"
        if not os.path.exists(db_path):
            print("❌ ملف قاعدة البيانات غير موجود!")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # إضافة أو تحديث ملاحظات التوجيهات
        test_notes = """
1. يجب على المترشح الحضور قبل موعد الامتحان بـ 30 دقيقة
2. إحضار بطاقة التعريف الوطنية أو جواز السفر
3. استخدام القلم الأزرق أو الأسود فقط
4. عدم استخدام الهاتف المحمول أثناء الامتحان
5. قراءة التعليمات بعناية قبل البدء في الإجابة
6. كتابة الاسم ورقم المترشح في جميع أوراق الإجابة
7. احترام الوقت المحدد للامتحان
8. عدم الغش أو محاولة النقل من الآخرين
        """.strip()
        
        # تحديث أو إدراج البيانات
        cursor.execute("""
            INSERT OR REPLACE INTO جدولة_الامتحان (id, ملاحظات)
            VALUES (1, ?)
        """, (test_notes,))
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء بيانات الاختبار بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء بيانات الاختبار: {e}")
        return False

def test_final_alignment():
    """اختبار نهائي لمحاذاة التوجيهات"""
    
    print("🔍 اختبار المحاذاة النهائية...")
    print("=" * 60)
    
    try:
        # إنشاء بيانات اختبار
        if not create_test_data():
            return False
        
        # استيراد ملف print12
        import print12
        
        print("✅ تم استيراد ملف print12.py بنجاح")
        
        # التحقق من وجود بيانات المترشحين
        db_path = "data.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM امتحانات")
        candidates_count = cursor.fetchone()[0]
        
        if candidates_count == 0:
            print("⚠️ لا توجد بيانات مترشحين في قاعدة البيانات")
            conn.close()
            return False
        
        print(f"✅ تم العثور على {candidates_count} مترشح في قاعدة البيانات")
        
        # التحقق من الملاحظات المحدثة
        cursor.execute("SELECT ملاحظات FROM جدولة_الامتحان WHERE id = 1")
        notes_result = cursor.fetchone()
        
        if notes_result and notes_result[0]:
            print(f"📝 الملاحظات الموجودة: {notes_result[0][:100]}...")
        else:
            print("⚠️ لا توجد ملاحظات في جدولة_الامتحان")
        
        conn.close()
        
        # إنشاء تقرير اختبار
        print("\n🧪 إنشاء تقرير اختبار المحاذاة...")
        
        test_reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'اختبار_المحاذاة_النهائي')
        os.makedirs(test_reports_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # اختبار الطباعة العادية (استدعاء واحد في الصفحة)
        success1, output_path1, message1 = print12.print_exams_report(
            parent=None,
            level=None,
            report_title="اختبار المحاذاة النهائية - طباعة عادية",
            sub_title=None,
            filter_criteria=None,
            output_dir=test_reports_dir,
            two_per_page=False  # طباعة عادية
        )
        
        if success1:
            print(f"✅ تم إنشاء تقرير الطباعة العادية بنجاح!")
            print(f"📁 مسار الملف: {output_path1}")
            
            if os.path.exists(output_path1):
                file_size = os.path.getsize(output_path1)
                print(f"📊 حجم الملف: {file_size} بايت")
        else:
            print(f"❌ فشل في إنشاء تقرير الطباعة العادية: {message1}")
        
        print("\n" + "="*60)
        print("🎯 نتائج الاختبار:")
        print("="*60)
        
        if success1:
            print("✅ تم إنشاء التقرير بنجاح")
            print("📋 التحقق المطلوب:")
            print("   1. افتح الملف المُنشأ")
            print("   2. ابحث عن قسم 'توجيهات عامة للمترشح'")
            print("   3. تأكد من أن العنوان محاذي إلى اليمين")
            print("   4. تأكد من أن النص محاذي إلى اليمين")
            print("   5. تأكد من أن النص مقسم على أسطر متعددة")
            print("   6. تأكد من عدم ظهور صورة (تم إعطاء الأولوية للنص)")
            
            print("\n🔧 التحسينات المطبقة:")
            print("   ✅ إعطاء الأولوية للنص بدلاً من الصورة")
            print("   ✅ محاذاة العنوان إلى اليمين")
            print("   ✅ محاذاة المحتوى إلى اليمين")
            print("   ✅ تقسيم النص الطويل إلى أسطر")
            print("   ✅ تباعد محسن بين الأسطر")
            
            return True
        else:
            print("❌ فشل في إنشاء التقرير")
            return False
            
    except ImportError as e:
        print(f"❌ خطأ في استيراد print12.py: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def check_image_path():
    """فحص مسار صورة التوجيهات"""
    
    print("\n🔍 فحص مسار صورة التوجيهات...")
    print("-" * 40)
    
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    app_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
    images_folder = os.path.join(app_folder, "صور التوجيهات")
    image_path = os.path.join(images_folder, "توجيهات_المترشح.png")
    
    print(f"📁 مسار الصورة المتوقع: {image_path}")
    
    if os.path.exists(image_path):
        print("⚠️ الصورة موجودة - لكن تم تعطيلها لإعطاء الأولوية للنص")
        file_size = os.path.getsize(image_path)
        print(f"📊 حجم الصورة: {file_size} بايت")
    else:
        print("ℹ️ الصورة غير موجودة - سيتم عرض النص فقط")
    
    print("✅ النظام الآن يعطي الأولوية للنص المحاذي إلى اليمين")

if __name__ == "__main__":
    print("🚀 بدء الاختبار النهائي لمحاذاة التوجيهات العامة للمترشحين")
    print("=" * 80)
    
    # فحص مسار الصورة
    check_image_path()
    
    # تشغيل الاختبار النهائي
    test_result = test_final_alignment()
    
    print("\n" + "=" * 80)
    print("📋 الخلاصة النهائية:")
    
    if test_result:
        print("🎉 نجح الاختبار!")
        print("✨ التوجيهات العامة للمترشحين تظهر الآن بمحاذاة صحيحة إلى اليمين")
        print("🔧 تم إعطاء الأولوية للنص بدلاً من الصورة")
        print("📖 يرجى فتح الملف المُنشأ للتحقق البصري النهائي")
    else:
        print("⚠️ فشل الاختبار. يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 80)
