#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار محاذاة التوجيهات العامة للمترشحين في print12.py
========================================================

هذا الملف يختبر أن التوجيهات العامة للمترشحين تظهر بمحاذاة صحيحة إلى اليمين.
"""

import os
import sys
import sqlite3
from datetime import datetime

def test_print12_alignment():
    """اختبار محاذاة التوجيهات في print12.py"""
    
    print("🔍 اختبار محاذاة التوجيهات العامة للمترشحين...")
    print("=" * 60)
    
    try:
        # استيراد ملف print12
        import print12
        
        print("✅ تم استيراد ملف print12.py بنجاح")
        
        # التحقق من وجود قاعدة البيانات
        db_path = "data.db"
        if not os.path.exists(db_path):
            print("❌ ملف قاعدة البيانات غير موجود!")
            return False
        
        print("✅ ملف قاعدة البيانات موجود")
        
        # التحقق من وجود بيانات في جدول الامتحانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM امتحانات")
        candidates_count = cursor.fetchone()[0]
        
        if candidates_count == 0:
            print("⚠️ لا توجد بيانات مترشحين في قاعدة البيانات")
            conn.close()
            return False
        
        print(f"✅ تم العثور على {candidates_count} مترشح في قاعدة البيانات")
        
        # التحقق من وجود بيانات في جدول جدولة_الامتحان
        cursor.execute("SELECT COUNT(*) FROM جدولة_الامتحان")
        schedule_count = cursor.fetchone()[0]
        
        print(f"📊 عدد سجلات جدولة الامتحان: {schedule_count}")
        
        # التحقق من وجود ملاحظات في جدولة_الامتحان
        cursor.execute("SELECT ملاحظات FROM جدولة_الامتحان WHERE id = 1")
        notes_result = cursor.fetchone()
        
        if notes_result and notes_result[0]:
            print(f"📝 توجد ملاحظات في جدولة_الامتحان: {notes_result[0][:50]}...")
        else:
            print("ℹ️ لا توجد ملاحظات في جدولة_الامتحان")
        
        conn.close()
        
        # اختبار إنشاء تقرير تجريبي
        print("\n🧪 اختبار إنشاء تقرير تجريبي...")
        
        # إنشاء مجلد التقارير التجريبية
        test_reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'اختبار_التوجيهات')
        os.makedirs(test_reports_dir, exist_ok=True)
        
        # تحديد اسم الملف التجريبي
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        test_output_path = os.path.join(test_reports_dir, f"اختبار_محاذاة_التوجيهات_{timestamp}.pdf")
        
        # استدعاء دالة إنشاء التقرير
        success, output_path, message = print12.print_exams_report(
            parent=None,
            level=None,
            report_title="اختبار محاذاة التوجيهات العامة للمترشحين",
            sub_title=None,
            filter_criteria=None,
            output_dir=test_reports_dir,
            two_per_page=False
        )
        
        if success:
            print(f"✅ تم إنشاء التقرير التجريبي بنجاح!")
            print(f"📁 مسار الملف: {output_path}")
            print(f"💬 رسالة: {message}")
            
            # التحقق من وجود الملف
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"📊 حجم الملف: {file_size} بايت")
                
                if file_size > 1000:  # إذا كان حجم الملف أكبر من 1KB
                    print("✅ الملف تم إنشاؤه بحجم مناسب")
                    
                    print("\n🎯 نصائح للتحقق من المحاذاة:")
                    print("1. افتح الملف المُنشأ")
                    print("2. ابحث عن قسم 'توجيهات عامة للمترشح'")
                    print("3. تأكد من أن النص محاذي إلى اليمين")
                    print("4. تأكد من أن النص مقروء ومنسق بشكل صحيح")
                    
                    return True
                else:
                    print("⚠️ حجم الملف صغير جداً، قد تكون هناك مشكلة")
                    return False
            else:
                print("❌ لم يتم إنشاء الملف رغم الإبلاغ عن النجاح")
                return False
        else:
            print(f"❌ فشل في إنشاء التقرير التجريبي: {message}")
            return False
            
    except ImportError as e:
        print(f"❌ خطأ في استيراد print12.py: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_arabic_pdf_class():
    """اختبار كلاس ArabicPDF ودالة multi_line_ar_text"""
    
    print("\n🔧 اختبار كلاس ArabicPDF...")
    print("-" * 40)
    
    try:
        import print12
        
        # إنشاء كائن من كلاس ArabicPDF
        pdf = print12.ArabicPDF()
        
        print("✅ تم إنشاء كائن ArabicPDF بنجاح")
        
        # اختبار دالة ar_text
        test_text = "توجيهات عامة للمترشح"
        ar_text_result = pdf.ar_text(test_text)
        
        if ar_text_result:
            print("✅ دالة ar_text تعمل بشكل صحيح")
        else:
            print("⚠️ دالة ar_text قد لا تعمل بشكل صحيح")
        
        # اختبار دالة multi_line_ar_text
        long_text = "هذا نص طويل للاختبار يجب أن يتم تقسيمه إلى عدة أسطر عندما يتجاوز العرض المحدد للخلية في ملف PDF"
        lines = pdf.multi_line_ar_text(long_text, 100, 12)
        
        print(f"✅ دالة multi_line_ar_text تعمل بشكل صحيح")
        print(f"📝 تم تقسيم النص إلى {len(lines)} سطر")
        
        for i, line in enumerate(lines, 1):
            print(f"   {i}. {line}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار كلاس ArabicPDF: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار محاذاة التوجيهات العامة للمترشحين")
    print("=" * 80)
    
    # تشغيل الاختبارات
    test1_result = test_arabic_pdf_class()
    test2_result = test_print12_alignment()
    
    print("\n" + "=" * 80)
    print("📋 ملخص النتائج:")
    print(f"   🔧 اختبار كلاس ArabicPDF: {'✅ نجح' if test1_result else '❌ فشل'}")
    print(f"   🧪 اختبار إنشاء التقرير: {'✅ نجح' if test2_result else '❌ فشل'}")
    
    if test1_result and test2_result:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✨ التوجيهات العامة للمترشحين يجب أن تظهر بمحاذاة صحيحة إلى اليمين.")
        print("📖 يرجى فتح الملف المُنشأ للتحقق البصري من المحاذاة.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    print("=" * 80)
