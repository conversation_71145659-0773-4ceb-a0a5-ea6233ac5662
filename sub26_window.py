"""
نافذة جدولة الامتحان الاحترافية
=================================

هذا الملف يحتوي على النافذة الرئيسية لجدولة الامتحانات.

** المصدر الوحيد للبيانات **
جدول 'جدولة_الامتحان' في قاعدة البيانات هو المصدر الوحيد والموثوق
لجميع بيانات جدولة الامتحانات في النظام.

جميع العمليات (الحفظ، التحميل، العرض، الطباعة) تتم من خلال هذا الجدول فقط.
أي ملفات أخرى تحتاج لبيانات جدولة الامتحانات يجب أن تستخدم هذا الجدول.
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QMessageBox, QTableWidgetItem
)
from PyQt5.QtGui import QFont
from PyQt5.QtCore import Qt
import sqlite3
import os
import subprocess
from datetime import datetime

from professional_exam_table import ProfessionalExamTable

class ExamScheduleProfessional(QDialog):
    """
    نافذة جدولة الامتحان الاحترافية

    هذه النافذة تدير جدولة الامتحانات باستخدام جدول 'جدولة_الامتحان'
    كمصدر وحيد للبيانات في قاعدة البيانات.
    """

    def __init__(self, parent=None, db_path="data.db"):
        super().__init__(parent)
        self.db_path = db_path
        self.ensure_table_exists()
        self.initUI()

    def initUI(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("جدولة الامتحان")
        self.setFixedSize(900, 620)
        self.setStyleSheet("""
            QDialog {
                background-color: white;
            }
        """)
        self.setLayoutDirection(Qt.RightToLeft)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # إضافة عنوان
        title_label = QLabel("جدولة الامتحان")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #0066CC;")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # إضافة جدول الامتحانات الاحترافي
        self.exam_table = ProfessionalExamTable(self)
        self.exam_table.setContentsMargins(5, 5, 5, 5)  # إزالة الهوامش
        main_layout.addWidget(self.exam_table)

        # تم إزالة زر تصدير إلى Excel



        # إضافة الأزرار في صف واحد
        buttons_layout = QHBoxLayout()

        # زر مسح البيانات
        clear_button = QPushButton("مسح البيانات")
        clear_button.setFont(QFont("Calibri", 13, QFont.Bold))
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #F39C12;
                color: white;
                border-radius: 5px;
                padding: 8px;
                min-width: 120px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #D68910;
            }
        """)
        clear_button.clicked.connect(self.clearData)

        # زر البيانات الافتراضية
        default_button = QPushButton("البيانات الافتراضية")
        default_button.setFont(QFont("Calibri", 13, QFont.Bold))
        default_button.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border-radius: 5px;
                padding: 8px;
                min-width: 150px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        default_button.clicked.connect(self.loadDefaultData)

        # زر طباعة الجدولة
        print_button = QPushButton("طباعة الجدولة")
        print_button.setFont(QFont("Calibri", 13, QFont.Bold))
        print_button.setStyleSheet("""
            QPushButton {
                background-color: #9B59B6;
                color: white;
                border-radius: 5px;
                padding: 8px;
                min-width: 150px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #8E44AD;
            }
        """)
        print_button.clicked.connect(self.printSchedule)

        # زر حفظ الجدولة
        save_button = QPushButton("حفظ الجدولة")
        save_button.setFont(QFont("Calibri", 13, QFont.Bold))
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #0066CC;
                color: white;
                border-radius: 5px;
                padding: 8px;
                min-width: 150px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #0055AA;
            }
        """)
        save_button.clicked.connect(self.saveSchedule)

        # زر إلغاء
        cancel_button = QPushButton("إلغاء")
        cancel_button.setFont(QFont("Calibri", 13, QFont.Bold))
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border-radius: 5px;
                padding: 8px;
                min-width: 120px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        cancel_button.clicked.connect(self.reject)

        # إضافة الأزرار إلى التخطيط
        buttons_layout.addWidget(clear_button)
        buttons_layout.addSpacing(15)  # مسافة بين الأزرار
        buttons_layout.addWidget(default_button)
        buttons_layout.addSpacing(15)  # مسافة بين الأزرار
        buttons_layout.addWidget(print_button)
        buttons_layout.addSpacing(15)  # مسافة بين الأزرار
        buttons_layout.addWidget(save_button)
        buttons_layout.addSpacing(15)  # مسافة بين الأزرار
        buttons_layout.addWidget(cancel_button)

        # إضافة مسافة قبل وبعد الأزرار للمحاذاة في الوسط
        buttons_layout.addStretch(1)
        buttons_layout.insertStretch(0, 1)

        # إضافة تخطيط الأزرار إلى التخطيط الرئيسي
        main_layout.addLayout(buttons_layout)

        # محاولة تحميل البيانات من قاعدة البيانات
        self.loadScheduleFromDB()

    # تم إزالة دالة exportToExcel

    def ensure_table_exists(self):
        """التأكد من وجود جدول جدولة_الامتحان وجدول مواد_الامتحان في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود جدول جدولة_الامتحان
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='جدولة_الامتحان'")
            table_exists = cursor.fetchone()

            if not table_exists:
                # إنشاء جدول جدولة_الامتحان إذا لم يكن موجودًا
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS جدولة_الامتحان (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        اليوم TEXT,
                        التاريخ TEXT,
                        الحصة1 TEXT,
                        التوقيت1 TEXT,
                        الحصة2 TEXT,
                        التوقيت2 TEXT,
                        الحصة3 TEXT,
                        التوقيت3 TEXT,
                        الحصة4 TEXT,
                        التوقيت4 TEXT,
                        السنة_الدراسية TEXT,
                        الأسدس TEXT,
                        تاريخ_التحديث TEXT,
                        ملاحظات TEXT
                    )
                """)

            # التحقق من وجود جدول مواد_الامتحان
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='مواد_الامتحان'")
            table_exists = cursor.fetchone()

            if not table_exists:
                # إنشاء جدول مواد_الامتحان إذا لم يكن موجودًا
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS مواد_الامتحان (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        المادة TEXT,
                        اليوم TEXT,
                        التاريخ TEXT,
                        التوقيت TEXT,
                        السنة_الدراسية TEXT,
                        الأسدس TEXT,
                        تاريخ_التحديث TEXT
                    )
                """)

            conn.commit()
            conn.close()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التحقق من وجود الجداول: {str(e)}")

    def createDefaultData(self):
        """إنشاء بيانات افتراضية للجدول"""
        # مسح الجدول الحالي
        self.exam_table.exam_table.setRowCount(0)

        # تعريف البيانات الافتراضية
        default_days = ["الاثنين", "الثلاثاء", "الأربعاء"]
        default_dates = ["2024-06-10", "2024-06-11", "2024-06-12"]

        # أسماء المواد الافتراضية
        subject_names = [
            "المادة الأولى", "المادة الثانية", "المادة الثالثة", "المادة الرابعة",
            "المادة الخامسة", "المادة السادسة", "المادة السابعة", "المادة الثامنة",
            "المادة التاسعة", "المادة العاشرة"
        ]

        # إضافة 3 صفوف افتراضية
        for i in range(3):
            row = self.exam_table.exam_table.rowCount()
            self.exam_table.exam_table.insertRow(row)

            # إضافة اليوم والتاريخ
            day_date = f"{default_days[i]} {default_dates[i]}"
            self.exam_table.exam_table.setItem(row, 0, QTableWidgetItem(day_date))

            # إضافة المواد والتوقيتات
            for col in range(1, 5):
                # اختيار اسم المادة (مادة مختلفة لكل خلية)
                subject_index = (i * 4) + (col - 1)
                if subject_index < len(subject_names):
                    subject_name = subject_names[subject_index]
                else:
                    subject_name = f"المادة {subject_index + 1}"

                # تحديد التوقيت حسب العمود
                if col == 1:
                    time_text = "08.00-10.00"
                elif col == 2:
                    time_text = "10.30-12.30"
                elif col == 3:
                    time_text = "14.30-16.30"
                else:
                    time_text = "17.00-19.00"

                self.exam_table.exam_table.setItem(row, col, QTableWidgetItem(f"{subject_name}\n({time_text})"))

            # تعيين ارتفاع الصف
            self.exam_table.exam_table.setRowHeight(row, 50)

    def clearData(self):
        """مسح جميع البيانات من الجدول"""
        # مسح الجدول الحالي
        self.exam_table.exam_table.setRowCount(0)

        # عرض رسالة نجاح
        QMessageBox.information(self, "نجاح", "تم مسح جميع البيانات بنجاح.")

    def loadDefaultData(self):
        """تحميل البيانات الافتراضية في الجدول"""
        # إنشاء البيانات الافتراضية
        self.createDefaultData()

        # عرض رسالة نجاح
        QMessageBox.information(self, "نجاح", "تم تحميل البيانات الافتراضية بنجاح.")

    def printSchedule(self):
        """طباعة جدولة الامتحان"""
        try:
            # استيراد print13 لاستخدام ArabicPDF
            import print13
            
            # إنشاء مسار للملف
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            reports_folder = os.path.join(desktop_path, "تقارير الامتحانات")

            # التأكد من وجود المجلد
            os.makedirs(reports_folder, exist_ok=True)

            # إنشاء اسم الملف
            current_date = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = f"جدولة_الامتحان_{current_date}.pdf"
            file_path = os.path.join(reports_folder, file_name)

            # إنشاء PDF باستخدام ArabicPDF بشكل أفقي
            pdf = print13.ArabicPDF('L', 'mm', 'A4')  # 'L' للوضع الأفقي
            pdf.add_page()
            y = pdf.get_y()

            # جلب الشعار من قاعدة البيانات
            try:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
                logo_row = cursor.fetchone()
                logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None
                conn.close()
            except:
                logo_path = None

            # إضافة الشعار
            if logo_path:
                logo_width = 70
                logo_height = 28
                x_logo = (pdf.w - logo_width) / 2
                pdf.image(logo_path, x=x_logo, y=y, w=logo_width, h=logo_height)
                y += logo_height + 10

            # العنوان الرئيسي
            pdf.set_font('Arial', 'B', 18)
            pdf.set_text_color(0, 0, 128)
            pdf.set_xy(10, y)
            pdf.cell(pdf.w - 20, 15, pdf.ar_text("جدولة الامتحان"), border=1, align='C')
            y += 20

            # إعدادات الجدول - زيادة عرض الأعمدة للاستفادة من الوضع الأفقي
            pdf.set_font('Arial', 'B', 14)
            pdf.set_text_color(0, 0, 0)

            # عرض الأعمدة مع زيادة العرض للاستفادة من الصفحة الأفقية
            col_widths = [57, 55, 55, 55, 55]  # زيادة عرض الأعمدة
            # عكس ترتيب العناوين
            headers = ["الحصة الرابعة", "الحصة الثالثة", "الحصة الثانية", "الحصة الأولى", "اليوم والتاريخ"]

            # رسم عناوين الأعمدة
            pdf.set_fill_color(200, 200, 200)
            x = 10
            for i, header in enumerate(headers):
                pdf.set_xy(x, y)
                pdf.cell(col_widths[i], 10, pdf.ar_text(header), border=1, align='C', fill=True)
                x += col_widths[i]

            y += 10
            pdf.set_font('Arial', '', 14)

            # رسم بيانات الجدول
            for row in range(self.exam_table.exam_table.rowCount()):
                x = 10
                row_height = 25  # زيادة ارتفاع الصف لاستيعاب سطرين

                # الحصول على بيانات الصف - عكس ترتيب البيانات
                row_data = []
                
                # اليوم والتاريخ (في النهاية بعد العكس)
                day_date_item = self.exam_table.exam_table.item(row, 0)
                day_date = day_date_item.text() if day_date_item else ""
                
                # الحصص الأربع - في ترتيب معكوس (من الرابعة إلى الأولى)
                sessions = []
                for col in range(4, 0, -1):  # من العمود 4 إلى 1
                    cell_item = self.exam_table.exam_table.item(row, col)
                    cell_text = cell_item.text() if cell_item else ""
                    sessions.append(cell_text)
                
                # ترتيب البيانات: الحصة الرابعة، الثالثة، الثانية، الأولى، ثم اليوم والتاريخ
                row_data = sessions + [day_date]

                # رسم الخلايا
                for i, cell_data in enumerate(row_data):
                    pdf.set_xy(x, y)
                    
                    # رسم إطار الخلية أولاً
                    pdf.cell(col_widths[i], row_height, '', border=1, align='C')
                    
                    # تحليل النص للحصول على المادة والتوقيت منفصلين
                    if cell_data and '\n' in cell_data and '(' in cell_data and ')' in cell_data:
                        lines = cell_data.split('\n')
                        if len(lines) >= 2:
                            subject = lines[0].strip()  # المادة
                            time = lines[1].strip('()').strip()  # التوقيت بدون الأقواس
                            
                            # رسم المادة في السطر الأول بالخط الأسود العادي
                            pdf.set_text_color(0, 0, 0)  # أسود
                            pdf.set_xy(x + 1, y + 3)
                            pdf.cell(col_widths[i] - 2, 8, pdf.ar_text(subject), border=0, align='C')
                            
                            # رسم التوقيت في السطر الثاني بخط أزرق غامق داخل قوسين
                            pdf.set_text_color(0, 0, 128)  # أزرق غامق
                            pdf.set_xy(x + 1, y + 13)
                            pdf.cell(col_widths[i] - 2, 8, pdf.ar_text(f"({time})"), border=0, align='C')
                            
                            # إعادة تعيين اللون للأسود للعناصر التالية
                            pdf.set_text_color(0, 0, 0)
                        else:
                            # إذا لم يكن التنسيق صحيحاً، اعرض النص كما هو
                            pdf.set_xy(x + 1, y + 8)
                            pdf.cell(col_widths[i] - 2, 8, pdf.ar_text(cell_data), border=0, align='C')
                    else:
                        # للخلايا التي لا تحتوي على مادة وتوقيت (مثل اليوم والتاريخ)
                        pdf.set_xy(x + 1, y + 8)
                        pdf.cell(col_widths[i] - 2, 8, pdf.ar_text(cell_data), border=0, align='C')
                    
                    x += col_widths[i]

                y += row_height

                # التحقق من الحاجة لصفحة جديدة
                if y > pdf.h - 30:
                    pdf.add_page()
                    y = pdf.get_y()

            # حفظ الملف
            pdf.output(file_path)

            # عرض رسالة نجاح
            QMessageBox.information(self, "نجاح", f"تم طباعة جدولة الامتحان بنجاح.\nتم حفظ الملف في: {file_path}")

            # فتح الملف
            try:
                import sys
                if sys.platform == 'win32':
                    os.startfile(file_path)
                elif sys.platform == 'darwin':  # macOS
                    subprocess.call(['open', file_path])
                else:  # Linux
                    subprocess.call(['xdg-open', file_path])
            except Exception as e:
                print(f"تعذر فتح الملف: {e}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء طباعة جدولة الامتحان: {str(e)}")

    def loadScheduleFromDB(self):
        """تحميل جدولة الامتحان من قاعدة البيانات - المصدر الوحيد للبيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الحصول على السنة الدراسية الحالية والأسدس
            cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            academic_year = result[0] if result and result[0] else ""
            semester = result[1] if result and result[1] else ""

            # استعلام عن جدولة الامتحان للسنة الدراسية والأسدس الحاليين
            # هذا هو المصدر الوحيد والموثوق لبيانات جدولة الامتحانات
            cursor.execute("""
                SELECT اليوم, التاريخ,
                       الحصة1, التوقيت1,
                       الحصة2, التوقيت2,
                       الحصة3, التوقيت3,
                       الحصة4, التوقيت4
                FROM جدولة_الامتحان
                WHERE السنة_الدراسية = ? AND الأسدس = ?
                ORDER BY id
            """, (academic_year, semester))

            schedule_data = cursor.fetchall()
            conn.close()

            # إذا وجدت بيانات، قم بتحميلها في الجدول
            if schedule_data:
                # مسح الجدول الحالي
                self.exam_table.exam_table.setRowCount(0)

                # إضافة البيانات من جدول جدولة_الامتحان فقط
                for row_data in schedule_data:
                    row = self.exam_table.exam_table.rowCount()
                    self.exam_table.exam_table.insertRow(row)

                    # إضافة اليوم والتاريخ (العمود الأول)
                    day_date = f"{row_data[0]} {row_data[1]}" if row_data[1] else row_data[0]
                    self.exam_table.exam_table.setItem(row, 0, QTableWidgetItem(day_date))

                    # إضافة المادة والتوقيت للحصة الأولى (العمود الثاني)
                    subject1 = row_data[2] or ""
                    time1 = row_data[3] or ""
                    if subject1 and time1:
                        self.exam_table.exam_table.setItem(row, 1, QTableWidgetItem(f"{subject1}\n({time1})"))
                    else:
                        self.exam_table.exam_table.setItem(row, 1, QTableWidgetItem(""))

                    # إضافة المادة والتوقيت للحصة الثانية (العمود الثالث)
                    subject2 = row_data[4] or ""
                    time2 = row_data[5] or ""
                    if subject2 and time2:
                        self.exam_table.exam_table.setItem(row, 2, QTableWidgetItem(f"{subject2}\n({time2})"))
                    else:
                        self.exam_table.exam_table.setItem(row, 2, QTableWidgetItem(""))

                    # إضافة المادة والتوقيت للحصة الثالثة (العمود الرابع)
                    subject3 = row_data[6] or ""
                    time3 = row_data[7] or ""
                    if subject3 and time3:
                        self.exam_table.exam_table.setItem(row, 3, QTableWidgetItem(f"{subject3}\n({time3})"))
                    else:
                        self.exam_table.exam_table.setItem(row, 3, QTableWidgetItem(""))

                    # إضافة المادة والتوقيت للحصة الرابعة (العمود الخامس)
                    subject4 = row_data[8] or ""
                    time4 = row_data[9] or ""
                    if subject4 and time4:
                        self.exam_table.exam_table.setItem(row, 4, QTableWidgetItem(f"{subject4}\n({time4})"))
                    else:
                        self.exam_table.exam_table.setItem(row, 4, QTableWidgetItem(""))

                    # تعيين ارتفاع الصف
                    self.exam_table.exam_table.setRowHeight(row, 50)

                return True
            else:
                # إذا لم توجد بيانات، قم بإنشاء بيانات افتراضية
                # وحفظها مباشرة في جدول جدولة_الامتحان
                self.createDefaultData()
                self.saveSchedule()  # حفظ البيانات الافتراضية في قاعدة البيانات
                return True
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل جدولة الامتحان من قاعدة البيانات: {str(e)}")
            # في حالة حدوث خطأ، قم بإنشاء بيانات افتراضية
            self.createDefaultData()
            return False

    def saveSchedule(self):
        """حفظ جدولة الامتحان في قاعدة البيانات - المصدر الوحيد للبيانات"""
        try:
            # الحصول على السنة الدراسية الحالية والأسدس
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            academic_year = result[0] if result and result[0] else ""
            semester = result[1] if result and result[1] else ""

            # استخراج البيانات من الجدول وحفظها في جدول جدولة_الامتحان
            # هذا هو المصدر الوحيد والموثوق لجميع بيانات جدولة الامتحانات
            current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # قائمة لتخزين جميع المواد المستخرجة لجدول مواد_الامتحان
            all_subjects = []

            for row in range(self.exam_table.exam_table.rowCount()):
                # الحصول على بيانات اليوم والتاريخ
                day_item = self.exam_table.exam_table.item(row, 0)
                day_text = day_item.text() if day_item else ""

                # فصل اليوم والتاريخ إذا كان ممكنًا
                day_parts = day_text.split(" ", 1)
                day = day_parts[0] if day_parts else ""
                date = day_parts[1] if len(day_parts) > 1 else ""

                # استخراج بيانات الحصة الأولى (العمود الثاني)
                subject1 = ""
                time1 = ""
                cell1_item = self.exam_table.exam_table.item(row, 1)
                if cell1_item and cell1_item.text():
                    cell1_text = cell1_item.text()
                    if "\n" in cell1_text and "(" in cell1_text and ")" in cell1_text:
                        parts = cell1_text.split("\n")
                        subject1 = parts[0]
                        time1 = parts[1].strip("()")
                        # إضافة المادة إلى القائمة إذا كانت غير فارغة
                        if subject1 and time1:
                            all_subjects.append((subject1, day, date, time1))

                # استخراج بيانات الحصة الثانية (العمود الثالث)
                subject2 = ""
                time2 = ""
                cell2_item = self.exam_table.exam_table.item(row, 2)
                if cell2_item and cell2_item.text():
                    cell2_text = cell2_item.text()
                    if "\n" in cell2_text and "(" in cell2_text and ")" in cell2_text:
                        parts = cell2_text.split("\n")
                        subject2 = parts[0]
                        time2 = parts[1].strip("()")
                        # إضافة المادة إلى القائمة إذا كانت غير فارغة
                        if subject2 and time2:
                            all_subjects.append((subject2, day, date, time2))

                # استخراج بيانات الحصة الثالثة (العمود الرابع)
                subject3 = ""
                time3 = ""
                cell3_item = self.exam_table.exam_table.item(row, 3)
                if cell3_item and cell3_item.text():
                    cell3_text = cell3_item.text()
                    if "\n" in cell3_text and "(" in cell3_text and ")" in cell3_text:
                        parts = cell3_text.split("\n")
                        subject3 = parts[0]
                        time3 = parts[1].strip("()")
                        # إضافة المادة إلى القائمة إذا كانت غير فارغة
                        if subject3 and time3:
                            all_subjects.append((subject3, day, date, time3))

                # استخراج بيانات الحصة الرابعة (العمود الخامس)
                subject4 = ""
                time4 = ""
                cell4_item = self.exam_table.exam_table.item(row, 4)
                if cell4_item and cell4_item.text():
                    cell4_text = cell4_item.text()
                    if "\n" in cell4_text and "(" in cell4_text and ")" in cell4_text:
                        parts = cell4_text.split("\n")
                        subject4 = parts[0]
                        time4 = parts[1].strip("()")
                        # إضافة المادة إلى القائمة إذا كانت غير فارغة
                        if subject4 and time4:
                            all_subjects.append((subject4, day, date, time4))

                # التحقق من وجود سجل بنفس اليوم والتاريخ
                cursor.execute("""
                    SELECT id FROM جدولة_الامتحان
                    WHERE اليوم = ? AND التاريخ = ? AND السنة_الدراسية = ? AND الأسدس = ?
                """, (day, date, academic_year, semester))

                existing_record = cursor.fetchone()

                if existing_record:
                    # تحديث السجل الموجود
                    cursor.execute("""
                        UPDATE جدولة_الامتحان
                        SET الحصة1 = ?, التوقيت1 = ?,
                            الحصة2 = ?, التوقيت2 = ?,
                            الحصة3 = ?, التوقيت3 = ?,
                            الحصة4 = ?, التوقيت4 = ?,
                            تاريخ_التحديث = ?
                        WHERE id = ?
                    """, (subject1, time1,
                          subject2, time2,
                          subject3, time3,
                          subject4, time4,
                          current_date, existing_record[0]))
                else:
                    # إضافة سجل جديد
                    cursor.execute("""
                        INSERT INTO جدولة_الامتحان (
                            اليوم, التاريخ,
                            الحصة1, التوقيت1,
                            الحصة2, التوقيت2,
                            الحصة3, التوقيت3,
                            الحصة4, التوقيت4,
                            السنة_الدراسية, الأسدس, تاريخ_التحديث
                        )
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (day, date,
                          subject1, time1,
                          subject2, time2,
                          subject3, time3,
                          subject4, time4,
                          academic_year, semester, current_date))

            # حفظ البيانات في جدول جدولة_الامتحان
            conn.commit()

            # حذف البيانات القديمة من جدول مواد_الامتحان
            cursor.execute("""
                DELETE FROM مواد_الامتحان
                WHERE السنة_الدراسية = ? AND الأسدس = ?
            """, (academic_year, semester))

            # إضافة المواد المستخرجة إلى جدول مواد_الامتحان
            for subject_data in all_subjects:
                subject, day, date, time = subject_data
                cursor.execute("""
                    INSERT INTO مواد_الامتحان (
                        المادة, اليوم, التاريخ, التوقيت,
                        السنة_الدراسية, الأسدس, تاريخ_التحديث
                    )
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (subject, day, date, time, academic_year, semester, current_date))

            conn.commit()
            conn.close()

            # عرض رسالة نجاح
            QMessageBox.information(self, "نجاح",
                "تم حفظ جدولة الامتحان بنجاح في قاعدة البيانات.\n"
                "جدول 'جدولة_الامتحان' هو المصدر الوحيد لجميع بيانات جدولة الامتحانات.\n"
                "تم أيضاً تحديث جدول 'مواد_الامتحان' للمواد المستخرجة.")

            # إغلاق النافذة
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ جدولة الامتحان في قاعدة البيانات: {str(e)}")

    def getScheduleFromDB(self):
        """
        دالة للحصول على بيانات جدولة الامتحان من قاعدة البيانات
        هذه الدالة يمكن استخدامها من قبل الملفات الأخرى للحصول على البيانات
        من المصدر الوحيد: جدول جدولة_الامتحان
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الحصول على السنة الدراسية الحالية والأسدس
            cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            academic_year = result[0] if result and result[0] else ""
            semester = result[1] if result and result[1] else ""

            # استعلام عن جدولة الامتحان - المصدر الوحيد
            cursor.execute("""
                SELECT اليوم, التاريخ,
                       الحصة1, التوقيت1,
                       الحصة2, التوقيت2,
                       الحصة3, التوقيت3,
                       الحصة4, التوقيت4,
                       السنة_الدراسية, الأسدس, تاريخ_التحديث, ملاحظات
                FROM جدولة_الامتحان
                WHERE السنة_الدراسية = ? AND الأسدس = ?
                ORDER BY id
            """, (academic_year, semester))

            schedule_data = cursor.fetchall()
            conn.close()
            return schedule_data
        except Exception as e:
            print(f"خطأ في الحصول على بيانات جدولة الامتحان: {str(e)}")
            return []

    @staticmethod
    def getScheduleFromDBStatic(db_path="data.db"):
        """
        دالة ثابتة للحصول على بيانات جدولة الامتحان من قاعدة البيانات
        يمكن استخدامها من قبل الملفات الأخرى دون إنشاء كائن من الكلاس
        المصدر الوحيد: جدول جدولة_الامتحان
        """
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # الحصول على السنة الدراسية الحالية والأسدس
            cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            academic_year = result[0] if result and result[0] else ""
            semester = result[1] if result and result[1] else ""

            # استعلام عن جدولة الامتحان - المصدر الوحيد
            cursor.execute("""
                SELECT اليوم, التاريخ,
                       الحصة1, التوقيت1,
                       الحصة2, التوقيت2,
                       الحصة3, التوقيت3,
                       الحصة4, التوقيت4,
                       السنة_الدراسية, الأسدس, تاريخ_التحديث, ملاحظات
                FROM جدولة_الامتحان
                WHERE السنة_الدراسية = ? AND الأسدس = ?
                ORDER BY id
            """, (academic_year, semester))

            schedule_data = cursor.fetchall()
            conn.close()
            return schedule_data
        except Exception as e:
            print(f"خطأ في الحصول على بيانات جدولة الامتحان: {str(e)}")
            return []


# تشغيل التطبيق كنافذة مستقلة للاختبار
if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)  # تعيين اتجاه التخطيط من اليمين إلى اليسار
    dialog = ExamScheduleProfessional()
    dialog.show()
    sys.exit(app.exec_())
