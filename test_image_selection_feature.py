#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ميزة اختيار صورة التوجيهات من المتصفح
===========================================
"""

import os
import sys
from datetime import datetime

def test_image_selection_feature():
    """اختبار ميزة اختيار الصورة الجديدة"""
    
    print("🔍 اختبار ميزة اختيار صورة التوجيهات من المتصفح...")
    print("=" * 70)
    
    # فحص مسار الصورة
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    app_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
    images_folder = os.path.join(app_folder, "صور التوجيهات")
    image_path = os.path.join(images_folder, "توجيهات_المترشح.png")
    
    print(f"📁 مسار الصورة المتوقع: {image_path}")
    
    # التحقق من وجود المجلد
    if os.path.exists(images_folder):
        print("✅ مجلد صور التوجيهات موجود")
    else:
        print("ℹ️ مجلد صور التوجيهات غير موجود (سيتم إنشاؤه عند الحاجة)")
    
    # التحقق من وجود الصورة الحالية
    if os.path.exists(image_path):
        print("✅ صورة التوجيهات موجودة")
        
        # معلومات الصورة
        file_size = os.path.getsize(image_path)
        modification_time = os.path.getmtime(image_path)
        mod_time_str = datetime.fromtimestamp(modification_time).strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"📊 حجم الصورة: {file_size} بايت")
        print(f"🕒 تاريخ آخر تعديل: {mod_time_str}")
        
        # تحديد نوع الصورة
        if file_size < 50000:  # أقل من 50KB
            print("🔍 نوع الصورة: صغيرة الحجم (ربما مُنشأة من النص)")
        elif file_size > 500000:  # أكبر من 500KB
            print("🔍 نوع الصورة: كبيرة الحجم (ربما صورة مختارة من الجهاز)")
        else:
            print("🔍 نوع الصورة: متوسطة الحجم")
    else:
        print("❌ صورة التوجيهات غير موجودة")
    
    return True

def show_feature_instructions():
    """عرض تعليمات استخدام الميزة الجديدة"""
    
    print("\n" + "=" * 70)
    print("📋 تعليمات استخدام الميزة الجديدة:")
    print("=" * 70)
    
    print("\n🎯 الميزة الجديدة تتيح لك خيارين:")
    print("   1️⃣ اختيار صورة جاهزة من جهازك")
    print("   2️⃣ إنشاء صورة تلقائياً من النص")
    
    print("\n🖼️ لاختيار صورة من الجهاز:")
    print("   1. افتح sub40_window.py")
    print("   2. اضغط على 'توجيهات عامة للمترشح (ة)'")
    print("   3. اضغط على 'اختيار صورة من الجهاز' (الزر الأزرق)")
    print("   4. اختر صورة من جهازك (PNG, JPG, JPEG, BMP, GIF)")
    print("   5. تأكد من رسالة النجاح")
    
    print("\n📝 لإنشاء صورة من النص:")
    print("   1. افتح sub40_window.py")
    print("   2. اضغط على 'توجيهات عامة للمترشح (ة)'")
    print("   3. اكتب أو عدّل النص في مربع التحرير")
    print("   4. اضغط على 'حفظ النص وإنشاء صورة' (الزر الأخضر)")
    print("   5. تأكد من رسالة النجاح")
    
    print("\n💡 نصائح مهمة:")
    print("   • استخدم صور عالية الجودة للحصول على أفضل نتيجة")
    print("   • تأكد من أن النص في الصورة محاذي إلى اليمين")
    print("   • تنسيق PNG هو الأفضل للنصوص العربية")
    print("   • يمكنك التبديل بين الطريقتين في أي وقت")
    
    print("\n🔄 اختبار النتيجة:")
    print("   1. أنشئ استدعاء من خلال sub40_window.py")
    print("   2. تأكد من ظهور الصورة في الاستدعاء")
    print("   3. تأكد من المحاذاة الصحيحة")

def check_supported_formats():
    """فحص تنسيقات الصور المدعومة"""
    
    print("\n📋 تنسيقات الصور المدعومة:")
    print("-" * 50)
    
    formats = [
        ("PNG", "الأفضل للنصوص والرسوم، يدعم الشفافية"),
        ("JPG/JPEG", "جيد للصور الفوتوغرافية، حجم أصغر"),
        ("BMP", "تنسيق غير مضغوط، جودة عالية"),
        ("GIF", "يدعم الشفافية، مناسب للرسوم البسيطة")
    ]
    
    for format_name, description in formats:
        print(f"   ✅ {format_name}: {description}")
    
    print("\n💡 التوصية: استخدم PNG للحصول على أفضل جودة للنصوص العربية")

def create_sample_instructions():
    """إنشاء توجيهات عينة للاختبار"""
    
    print("\n📝 إنشاء توجيهات عينة للاختبار...")
    print("-" * 50)
    
    sample_text = """
توجيهات عامة للمترشح (ة)

🕐 قبل الامتحان:
• الحضور قبل موعد الامتحان بـ 30 دقيقة
• إحضار بطاقة التعريف الوطنية أو جواز السفر
• إحضار الأدوات المطلوبة (أقلام، مسطرة، آلة حاسبة إذا سُمح بها)

✍️ أثناء الامتحان:
• قراءة التعليمات بعناية قبل البدء
• كتابة الاسم ورقم المترشح في جميع الأوراق
• استخدام القلم الأزرق أو الأسود فقط
• عدم استخدام الهاتف المحمول أو أي جهاز إلكتروني

⚠️ تحذيرات مهمة:
• عدم الغش أو محاولة النقل من الآخرين
• احترام الوقت المحدد للامتحان
• رفع اليد عند الحاجة لأي استفسار
• تسليم جميع الأوراق قبل مغادرة القاعة

📞 للاستفسار: اتصل بإدارة الامتحانات
    """.strip()
    
    try:
        import sqlite3
        db_path = "data.db"
        
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود سجل
            cursor.execute("SELECT id FROM جدولة_الامتحان WHERE id = 1")
            record = cursor.fetchone()
            
            if record:
                cursor.execute("UPDATE جدولة_الامتحان SET ملاحظات = ? WHERE id = 1", (sample_text,))
                print("✅ تم تحديث التوجيهات العينة في قاعدة البيانات")
            else:
                cursor.execute("INSERT INTO جدولة_الامتحان (id, ملاحظات) VALUES (1, ?)", (sample_text,))
                print("✅ تم إنشاء التوجيهات العينة في قاعدة البيانات")
            
            conn.commit()
            conn.close()
            
            print("💡 يمكنك الآن فتح نافذة التوجيهات لرؤية النص العينة")
            return True
        else:
            print("❌ ملف قاعدة البيانات غير موجود")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء التوجيهات العينة: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار ميزة اختيار صورة التوجيهات من المتصفح")
    print("=" * 70)
    
    # اختبار الحالة الحالية
    test_image_selection_feature()
    
    # فحص تنسيقات الصور المدعومة
    check_supported_formats()
    
    # إنشاء توجيهات عينة
    sample_created = create_sample_instructions()
    
    # عرض التعليمات
    show_feature_instructions()
    
    print("\n" + "=" * 70)
    print("📋 ملخص الحالة:")
    
    if sample_created:
        print("✅ تم إنشاء توجيهات عينة للاختبار")
    else:
        print("⚠️ لم يتم إنشاء توجيهات عينة")
    
    print("🎯 الميزة الجديدة جاهزة للاستخدام!")
    print("📖 اتبع التعليمات أعلاه لاختبار الميزة")
    print("=" * 70)
