#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إنشاء صورة التوجيهات بمحاذاة صحيحة
==========================================
"""

import os
import sys
import sqlite3
from datetime import datetime

def test_image_creation():
    """اختبار إنشاء صورة التوجيهات"""
    
    print("🔍 اختبار إنشاء صورة التوجيهات بمحاذاة صحيحة...")
    print("=" * 70)
    
    # فحص مسار الصورة
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    app_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
    images_folder = os.path.join(app_folder, "صور التوجيهات")
    image_path = os.path.join(images_folder, "توجيهات_المترشح.png")
    
    print(f"📁 مسار الصورة المتوقع: {image_path}")
    
    # التحقق من وجود قاعدة البيانات
    db_path = "data.db"
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود!")
        return False
    
    print("✅ ملف قاعدة البيانات موجود")
    
    # التحقق من وجود بيانات التوجيهات
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT ملاحظات FROM جدولة_الامتحان WHERE id = 1")
        result = cursor.fetchone()
        
        if result and result[0]:
            print(f"📝 توجد ملاحظات في قاعدة البيانات: {result[0][:100]}...")
            notes_exist = True
        else:
            print("ℹ️ لا توجد ملاحظات في قاعدة البيانات")
            notes_exist = False
        
        conn.close()
    except Exception as e:
        print(f"❌ خطأ في قراءة قاعدة البيانات: {e}")
        return False
    
    # التحقق من وجود الصورة
    if os.path.exists(image_path):
        print("✅ الصورة موجودة")
        
        # معلومات الصورة
        file_size = os.path.getsize(image_path)
        modification_time = os.path.getmtime(image_path)
        mod_time_str = datetime.fromtimestamp(modification_time).strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"📊 حجم الصورة: {file_size} بايت")
        print(f"🕒 تاريخ آخر تعديل: {mod_time_str}")
        
        # التحقق من أن الصورة حديثة (خلال آخر 24 ساعة)
        current_time = datetime.now().timestamp()
        if (current_time - modification_time) < 86400:  # 24 ساعة
            print("✅ الصورة حديثة (تم إنشاؤها خلال آخر 24 ساعة)")
        else:
            print("⚠️ الصورة قديمة (أكثر من 24 ساعة)")
        
        return True
    else:
        print("❌ الصورة غير موجودة")
        
        if notes_exist:
            print("💡 نصيحة: افتح نافذة التوجيهات من sub40_window.py واضغط 'حفظ التوجيهات' لإنشاء الصورة")
        else:
            print("💡 نصيحة: أضف نص التوجيهات أولاً ثم احفظ لإنشاء الصورة")
        
        return False

def create_test_instructions():
    """إنشاء توجيهات تجريبية"""
    
    print("\n📝 إنشاء توجيهات تجريبية...")
    print("-" * 50)
    
    test_instructions = """
توجيهات عامة للمترشح (ة)

1. يجب على المترشح الحضور قبل موعد الامتحان بـ 30 دقيقة على الأقل
2. إحضار بطاقة التعريف الوطنية أو جواز السفر ساري المفعول
3. استخدام القلم الأزرق أو الأسود فقط في الكتابة
4. عدم استخدام الهاتف المحمول أو أي جهاز إلكتروني أثناء الامتحان
5. قراءة التعليمات والأسئلة بعناية قبل البدء في الإجابة
6. كتابة الاسم الكامل ورقم المترشح في جميع أوراق الإجابة
7. احترام الوقت المحدد للامتحان وعدم تجاوزه
8. عدم الغش أو محاولة النقل من المترشحين الآخرين
9. رفع اليد عند الحاجة لأي استفسار أو مساعدة
10. تسليم جميع أوراق الإجابة قبل مغادرة القاعة

ملاحظة: أي مخالفة لهذه التوجيهات قد تؤدي إلى إلغاء الامتحان
    """.strip()
    
    try:
        db_path = "data.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود سجل
        cursor.execute("SELECT id FROM جدولة_الامتحان WHERE id = 1")
        record = cursor.fetchone()
        
        if record:
            # تحديث السجل الموجود
            cursor.execute("UPDATE جدولة_الامتحان SET ملاحظات = ? WHERE id = 1", (test_instructions,))
            print("✅ تم تحديث التوجيهات التجريبية")
        else:
            # إنشاء سجل جديد
            cursor.execute("INSERT INTO جدولة_الامتحان (id, ملاحظات) VALUES (1, ?)", (test_instructions,))
            print("✅ تم إنشاء التوجيهات التجريبية")
        
        conn.commit()
        conn.close()
        
        print("💡 يمكنك الآن فتح نافذة التوجيهات من sub40_window.py لرؤية النتيجة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التوجيهات التجريبية: {e}")
        return False

def show_instructions():
    """عرض التعليمات للمستخدم"""
    
    print("\n" + "=" * 70)
    print("📋 تعليمات الاختبار:")
    print("=" * 70)
    
    print("1. 🖥️  افتح ملف sub40_window.py")
    print("2. 🖱️  اضغط على زر 'توجيهات عامة للمترشح (ة)'")
    print("3. ✏️  اكتب أو عدّل النص في مربع التحرير")
    print("4. 👀  تأكد من أن النص محاذي إلى اليمين")
    print("5. 💾  اضغط على 'حفظ التوجيهات'")
    print("6. ✅  تأكد من ظهور رسالة النجاح")
    print("7. 📁  افتح مجلد الصور للتحقق من الصورة المُنشأة")
    print("8. 🔍  تأكد من أن النص في الصورة محاذي إلى اليمين")
    
    print("\n📁 مسار الصورة:")
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    app_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
    images_folder = os.path.join(app_folder, "صور التوجيهات")
    print(f"   {images_folder}")
    
    print("\n🎯 النتيجة المتوقعة:")
    print("   ✅ النص في مربع التحرير محاذي إلى اليمين")
    print("   ✅ الصورة المُنشأة تحتوي على نص محاذي إلى اليمين")
    print("   ✅ الصورة تظهر بشكل صحيح في الاستدعاءات")

if __name__ == "__main__":
    print("🚀 بدء اختبار إنشاء صورة التوجيهات بمحاذاة صحيحة")
    print("=" * 70)
    
    # اختبار وجود الصورة الحالية
    image_exists = test_image_creation()
    
    # إنشاء توجيهات تجريبية إذا لم تكن موجودة
    if not image_exists:
        create_success = create_test_instructions()
        if create_success:
            print("\n✨ تم إنشاء توجيهات تجريبية بنجاح!")
    
    # عرض التعليمات
    show_instructions()
    
    print("\n" + "=" * 70)
    print("📋 ملخص الحالة:")
    
    if image_exists:
        print("✅ الصورة موجودة ويمكن اختبارها")
        print("🔍 افتح الصورة للتحقق من المحاذاة")
    else:
        print("⚠️ الصورة غير موجودة")
        print("📝 اتبع التعليمات أعلاه لإنشاء الصورة")
    
    print("=" * 70)
