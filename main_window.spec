# -*- mode: python ; coding: utf-8 -*-

block_cipher = None
datas=[
    # الأيقونة الرئيسية
    ('01.ico', '.'),
    
    # ملفات النوافذ الأساسية المطلوبة فقط
    ('main_window.py', '.'),
    ('app.py', '.'),
    ('sub23_window.py', '.'),  # نافذة استيراد البيانات من Excel
    ('sub40_window.py', '.'),  # نافذة إدارة التقارير واللوائح والاستدعاءات
    ('sub20_window.py', '.'),  # مطلوبة لـ sub23_window
    
    # ملفات الطباعة المطلوبة
    ('print11.py', '.'),      # لوائح الحضور
    ('print12.py', '.'),      # استدعاءات المترشحين (عادي)
    ('print13.py', '.'),      # لوائح المترشحين
    ('print14.py', '.'),      # استدعاءات المترشحين (أفقي)
    ('print15.py', '.'),      # تقارير الإحصائيات
    ('print313.py', '.'),     # المحضر الجماعي
    
    # مجلد الخطوط (ضروري للطباعة العربية)
    ('fonts/', 'fonts/'),
    
    # مجلد اللوجات
    ('logs/', 'logs/'),
]

a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        # النوافذ الأساسية المطلوبة فقط
        'main_window',
        'app',
        'sub23_window',
        'sub40_window', 
        'sub20_window',
        
        # وحدات الطباعة المطلوبة فقط
        'print11',
        'print12',
        'print13',
        'print14',
        'print15',
        'print313',
        
        # مكتبات أساسية للواجهة الرسومية
        'PyQt5.QtWidgets',
        'PyQt5.QtGui',
        'PyQt5.QtCore',
        
        # مكتبات قاعدة البيانات
        'sqlite3',
        
        # مكتبات معالجة Excel
        'pandas',
        'openpyxl',
        'xlrd',
        
        # مكتبات PDF والطباعة العربية
        'fpdf',
        'arabic_reshaper',
        'bidi.algorithm',
        
        # مكتبات معالجة الصور (للشعارات)
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'PIL.ImageFont',
        
        # مكتبات النظام الأساسية
        'os',
        'sys',
        'datetime',
        'time',
        'pathlib',
        're',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # استبعاد المكتبات غير المطلوبة
        'tkinter',
        'matplotlib',
        'numpy',
        'scipy',
        'tensorflow',
        'torch',
        'jupyter',
        'IPython',
        'notebook',
        'plotly',
        'bokeh',
        'seaborn',
        'statsmodels',
        'sklearn',
        'cv2',
        'pygame',
        'kivy',
        'flask',
        'django',
        'requests',
        'urllib3',
        'selenium',
        'beautifulsoup4',
        'scrapy',
        'networkx',
        'sympy',
        'xlwings',
        'win32com',
        'pywintypes',
        'pythoncom',
        # استبعاد النوافذ غير المستخدمة
        'sub0_window',
        'sub1_window',
        'sub2_window',
        'sub3_window',
        'sub4_window',
        'sub5_window',
        'sub6_window',
        'sub7_window',
        'sub8_window',
        'sub9_window',
        'sub10_window',
        'sub11_window',
        'sub12_window',
        'sub13_window',
        'sub14_window',
        'sub15_window',
        'sub16_window',
        'sub17_window',
        'sub18_window',
        'sub19_window',
        'sub21_window',
        'sub22_window',
        'sub24_window',
        'sub25_window',
        'sub26_window',
        'sub27_window',
        'sub100_window',
        # استبعاد ملفات الطباعة غير المستخدمة
        'print_test',
        'print0',
        'print1',
        'print2',
        'print2_test',
        'print3',
        'print4',
        'print5',
        'print5_test',
        'print6',
        'print7',
        'print8',
        'print9',
        'print10',
        'thermal_image_print',
        'thermal_printer',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# إنشاء الملف التنفيذي بحجم مُحسن
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='برنامج_إدارة_الامتحانات',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,          # إزالة رموز التصحيح لتقليل الحجم
    upx=True,           # ضغط الملفات
    console=False,      # إخفاء نافذة موجه الأوامر
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch='x86_64',  # بنية 64 بت للأداء الأفضل
    codesign_identity=None,
    entitlements_file=None,
    icon='01.ico',
)

# جمع جميع الملفات في مجلد واحد مُحسن
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=True,         # إزالة الرموز غير الضرورية
    upx=True,          # ضغط إضافي
    upx_exclude=[
        'vcruntime140.dll',  # استبعاد DLL مهمة من الضغط
        'python*.dll',
        'Qt5*.dll',
    ],
    name='برنامج_إدارة_الامتحانات'
)
