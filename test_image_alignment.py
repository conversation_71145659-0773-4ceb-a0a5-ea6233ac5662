#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار محاذاة صورة التوجيهات إلى اليمين
=====================================
"""

import os
import sys
from datetime import datetime

def test_image_alignment():
    """اختبار محاذاة صورة التوجيهات"""
    
    print("🔍 اختبار محاذاة صورة التوجيهات إلى اليمين...")
    print("=" * 60)
    
    try:
        # استيراد ملف print12
        import print12
        print("✅ تم استيراد ملف print12.py بنجاح")
        
        # فحص مسار الصورة
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        app_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
        images_folder = os.path.join(app_folder, "صور التوجيهات")
        image_path = os.path.join(images_folder, "توجيهات_المترشح.png")
        
        print(f"📁 مسار الصورة: {image_path}")
        
        if os.path.exists(image_path):
            print("✅ الصورة موجودة - ستظهر محاذية إلى اليمين")
            file_size = os.path.getsize(image_path)
            print(f"📊 حجم الصورة: {file_size} بايت")
        else:
            print("ℹ️ الصورة غير موجودة - سيتم عرض النص من قاعدة البيانات")
        
        # إنشاء تقرير اختبار
        test_reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'اختبار_محاذاة_الصورة')
        os.makedirs(test_reports_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        success, output_path, message = print12.print_exams_report(
            parent=None,
            report_title="اختبار محاذاة صورة التوجيهات إلى اليمين",
            output_dir=test_reports_dir,
            two_per_page=False
        )
        
        if success:
            print(f"\n✅ تم إنشاء التقرير بنجاح!")
            print(f"📁 مسار الملف: {output_path}")
            
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"📊 حجم الملف: {file_size} بايت")
                
                print("\n🎯 التحقق المطلوب:")
                print("1. افتح الملف المُنشأ")
                print("2. ابحث عن قسم 'توجيهات عامة للمترشح'")
                print("3. تأكد من أن العنوان محاذي إلى اليمين")
                
                if os.path.exists(image_path):
                    print("4. تأكد من أن الصورة محاذية إلى اليمين")
                    print("5. تأكد من وضوح الإطار الأزرق حول الصورة")
                else:
                    print("4. تأكد من أن النص محاذي إلى اليمين")
                    print("5. تأكد من تقسيم النص على أسطر متعددة")
                
                return True
            else:
                print("❌ لم يتم إنشاء الملف")
                return False
        else:
            print(f"❌ فشل في إنشاء التقرير: {message}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار محاذاة صورة التوجيهات")
    print("=" * 60)
    
    result = test_image_alignment()
    
    print("\n" + "=" * 60)
    if result:
        print("🎉 نجح الاختبار!")
        print("✨ صورة التوجيهات تظهر الآن محاذية إلى اليمين")
    else:
        print("⚠️ فشل الاختبار")
    print("=" * 60)
