import os
import sys
import sqlite3
import traceback
from datetime import datetime
import subprocess

# ================= إعدادات عامة =================
# الجدول الأول: صفان، 4 أعمدة بدلاً من 6
COL_WIDTHS_TABLE1 = [70, 30, 70, 25]
# الجدول الثاني: 4 أعمدة مع صفين (تعديل)
COL_WIDTHS_TABLE2 = [50, 50, 40, 55]
# تعديل عناوين الجدول الثاني
TABLE2_HEADERS = ['الغائبون', 'الاسم والنسب', 'التوقيع', 'الرقم الوطني', 'الاسم الكامل', 'رقم الامتحان']

# إضافة ارتفاعات الصفوف
ROW_HEIGHT_TABLE1 = 8  # ارتفاع صفوف الجدول الأول
ROW_HEIGHT_TABLE2 = 6   # ارتفاع صفوف الجدول الثاني (جدول المترشحين) - سيتم تعديله ديناميكيًا حسب عدد الصفوف
ROW_HEIGHT_HEADER = 10  # ارتفاع صفوف الرأس
ROW_HEIGHT_TABLE_HEADER = 12  # ارتفاع صف رأس الجدول

# إضافة إعدادات للهوامش (بالملليمتر)
PAGE_MARGIN_TOP = 0.2     # الهامش العلوي للصفحة
PAGE_MARGIN_BOTTOM = 0.2  # الهامش السفلي للصفحة
PAGE_MARGIN_LEFT = 10     # الهامش الأيسر للصفحة
PAGE_MARGIN_RIGHT = 10    # الهامش الأيمن للصفحة

PT_TO_MM = 0.3528
LOGO_W_PT, LOGO_H_PT = 200, 80
BOX1_W_PT, BOX2_W_PT = 380, 170  # هنا يمكنك تعديل عرض مربع العنوان (BOX1_W_PT)
TITLE_H_PT = 40
LOGO_W = LOGO_W_PT * PT_TO_MM
LOGO_H = LOGO_H_PT * PT_TO_MM
BOX1_W = BOX1_W_PT * PT_TO_MM  # هذا المتغير يحول عرض البكسل إلى ملليمتر
BOX2_W = BOX2_W_PT * PT_TO_MM
BOX_H = TITLE_H_PT * PT_TO_MM

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display

class ArabicPDF(FPDF):
    def __init__(self, orientation='P', unit='mm', format='A4'):
        super().__init__(orientation, unit, format)
        # تعيين الهوامش
        self.set_margins(PAGE_MARGIN_LEFT, PAGE_MARGIN_TOP, PAGE_MARGIN_RIGHT)
        self.set_auto_page_break(auto=True, margin=PAGE_MARGIN_BOTTOM)
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        # تصحيح تحذير Deprecation Warning بإزالة المعامل uni=True
        self.add_font('Arial', '', os.path.join(fonts_dir, 'arial.ttf'))
        self.add_font('Arial', 'B', os.path.join(fonts_dir, 'arialbd.ttf'))
        self.set_font('Arial', '', 12)
        # تعيين سمك الخط الافتراضي
        self.set_line_width(0.4)

    def ar_text(self, txt: str) -> str:
        """
        تحويل النص العربي ليتم عرضه بشكل صحيح
        إذا كان النص يحتوي على رمز \n سيتم تجاهله وإرجاع نص مباشرة
        لأن fpdf تتعامل مع السطور الجديدة بشكل مختلف
        """
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

    def multi_line_ar_text(self, txt: str, cell_width: float, font_size: int = 12) -> list:
        """
        تقسيم النص إلى سطور لتتناسب مع عرض الخلية

        المعاملات:
            txt: النص المراد تقسيمه
            cell_width: عرض الخلية بالملليمتر
            font_size: حجم الخط

        العوائد:
            قائمة بأسطر النص بعد التقسيم
        """
        lines = []
        # تقسيم النص إلى كلمات
        words = txt.split(' ')
        current_line = ""

        for word in words:
            # تقدير عرض السطر الحالي مع الكلمة المضافة
            test_line = current_line + " " + word if current_line else word
            # تحويل مؤقت للنص العربي لحساب العرض بشكل صحيح
            ar_test_line = self.ar_text(test_line)

            # حساب عرض النص التقريبي (استخدام تقدير بسيط)
            self.set_font('Arial', '', font_size)
            width = self.get_string_width(ar_test_line)

            # إذا تجاوز العرض المسموح، نضيف السطر الحالي ونبدأ بسطر جديد
            if width > cell_width and current_line:
                lines.append(current_line)
                current_line = word
            else:
                current_line = test_line

        # إضافة السطر الأخير إذا كان غير فارغ
        if current_line:
            lines.append(current_line)

        return lines


def fetch_records(db_path: str):
    conn = sqlite3.connect(db_path)
    cur = conn.cursor()
    # جلب شعار المؤسسة
    cur.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
    logo_row = cur.fetchone()
    logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None

    # جلب بيانات الامتحانات مع تعديل الترتيب: رقم الامتحان أولاً ثم رقم القاعة رقمياً
    # إضافة عمود تاريخ_الازدياد للاستخدام في الجدول
    query = '''
    SELECT رقم_الامتحان, الاسم_الكامل, الرمز, مركز_الامتحان, المستوى, القسم, القاعة, تاريخ_الازدياد
    FROM امتحانات
    ORDER BY CAST(رقم_الامتحان AS INTEGER), CAST(القاعة AS INTEGER)
    '''
    cur.execute(query)
    cols = [c[0] for c in cur.description]
    recs = [dict(zip(cols, row)) for row in cur.fetchall()]
    conn.close()
    return logo_path, recs


def generate_report(logo_path, records, output_path, report_title=None, subject_data=None):
    pdf = ArabicPDF()
    margin = 10
    usable_w = pdf.w - 2 * margin

    # إذا لم يتم تحديد عنوان التقرير، نبحث عنه في قاعدة البيانات
    if not report_title:
        try:
            # البحث عن العنوان2 في جدول_الامتحان
            db_path = os.path.join(os.path.dirname(__file__), 'data.db')
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT العنوان2 FROM جدول_الامتحان LIMIT 1")
            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                report_title = result[0]
        except Exception as e:
            print(f"خطأ في استرجاع عنوان التقرير من قاعدة البيانات: {str(e)}")

    # تجميع حسب المستوى مع الحفاظ على الترتيب حسب رقم الامتحان
    levels = {}
    for rec in records:
        level = rec.get('المستوى','')
        levels.setdefault(level, []).append(rec)

    # ترتيب المستويات أبجدياً
    sorted_levels = sorted(levels.keys())

    # تحديد ارتفاع صفوف جدول المترشحين بناءً على عدد الصفوف
    global ROW_HEIGHT_TABLE2
    max_records_per_level = max([len(levels[level]) for level in levels]) if levels else 0

    # تعديل ارتفاع الصفوف بناءً على عدد المترشحين
    if max_records_per_level <= 20:
        ROW_HEIGHT_TABLE2 = 10  # ارتفاع أكبر للصفوف إذا كان عدد المترشحين 20 أو أقل
    elif 20 < max_records_per_level <= 25:
        ROW_HEIGHT_TABLE2 = 10  # ارتفاع متوسط للصفوف إذا كان عدد المترشحين بين 21 و 25
    elif 25 < max_records_per_level <= 30:
        ROW_HEIGHT_TABLE2 = 10  # ارتفاع أصغر للصفوف إذا كان عدد المترشحين بين 26 و 30
    else:
        ROW_HEIGHT_TABLE2 = 10  # ارتفاع أصغر للصفوف إذا كان عدد المترشحين أكثر من 30

    for level in sorted_levels:
        recs = levels[level]
        # ترتيب السجلات داخل كل مستوى حسب رقم الامتحان
        recs.sort(key=lambda x: int(x.get('رقم_الامتحان', '0')))

        pdf.add_page()
        y = pdf.get_y()
        # إضافة الشعار
        if logo_path:
            x_logo = (pdf.w - LOGO_W) / 2
            pdf.image(logo_path, x=x_logo, y=y, w=LOGO_W, h=LOGO_H)
        y += LOGO_H + 5

        # مربعات: العنوان فقط بدون المستوى
        pdf.set_draw_color(0,0,255)
        pdf.set_line_width(0.4)  # تعديل سمك الخط

        # تحديد ارتفاع موحد للمربعات العلوية
        FIXED_BOX_HEIGHT = 11  # ارتفاع موحد للعناوين

        x = margin
        # استخدام عنوان التقرير المخصص إذا كان متاحاً
        title_text = report_title if report_title else 'المحضر الجماعي للمترشحين'

        # تعيين لون أزرق غامق للخط
        pdf.set_text_color(0, 0, 128)  # أزرق غامق (RGB: 0, 0, 128)

        # عرض العنوان فقط في مربع واحد يغطي العرض الكامل
        pdf.set_xy(x, y)
        pdf.set_font('Arial','B', 14)
        # استخدام العرض الكامل للعنوان
        pdf.cell(BOX1_W + BOX2_W, FIXED_BOX_HEIGHT, pdf.ar_text(title_text), border=1, align='C')

        # إعادة لون النص إلى الأسود للعناصر الأخرى
        pdf.set_text_color(0, 0, 0)
        pdf.set_font('Arial','',12)
        y += FIXED_BOX_HEIGHT + 5

        # الجدول الثاني (المعدل): 4 أعمدة، 2 صفوف
        cols1 = COL_WIDTHS_TABLE1
        
        # صفوف الجدول الثاني المعدل
        row1 = [recs[0].get('مركز_الامتحان',''), 'مركز الامتحان', level, 'المستوى']
        row2 = ['', 'عدد الغائبين', str(len(recs)), 'عدد المترشحين']
        
        pdf.set_font('Arial','B',12)
        pdf.set_fill_color(230,230,230)
        
        # رسم الصف الأول
        x = margin
        for i, cell in enumerate(row1):
            pdf.set_xy(x, y)
            # استخدام تعبئة خلفية فقط للعناوين (الخلايا في موضع زوجي) - الخلايا في موضع فردي هي القيم
            fill = i % 2 == 1
            # محاذاة للوسط للعناوين، ويمين للقيم
            align = 'C' if i % 2 == 1 else 'R'
            pdf.cell(cols1[i], ROW_HEIGHT_TABLE1, pdf.ar_text(cell), border=1, align=align, fill=fill)
            x += cols1[i]
            
        # رسم الصف الثاني
        y += ROW_HEIGHT_TABLE1
        x = margin
        for i, cell in enumerate(row2):
            pdf.set_xy(x, y)
            # استخدام تعبئة خلفية فقط للعناوين (الخلايا في موضع زوجي) - الخلايا في موضع فردي هي القيم
            fill = i % 2 == 1
            # محاذاة للوسط للعناوين، ويمين للقيم
            align = 'C' if i % 2 == 1 else 'R'
            pdf.cell(cols1[i], ROW_HEIGHT_TABLE1, pdf.ar_text(cell), border=1, align=align, fill=fill)
            x += cols1[i]
            
        # تقليل المسافة بين الجدول الأول والثاني من 12 إلى 3 نقاط
        y += ROW_HEIGHT_TABLE1 + 3  # تعديل من 12 إلى 3
        
        # الجدول الثالث (جدول المترشحين)
        cols3 = [40, 30, 30, 40, 40, 15]  # تعديل عرض الأعمدة بشكل مناسب
        TABLE3_HEADERS = ['القاعة', 'تاريخ الازدياد', 'الرقم الوطني', 'الاسم الكامل', 'رقم الامتحان', 'ر.ت']
        
        pdf.set_font('Arial','B',12)
        pdf.set_fill_color(200,200,200)

        # رسم عناوين الأعمدة
        x = margin
        for i, header in enumerate(TABLE3_HEADERS):
            pdf.set_xy(x, y)
            pdf.cell(cols3[i], ROW_HEIGHT_TABLE_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
            x += cols3[i]

        y += ROW_HEIGHT_TABLE_HEADER
        pdf.set_font('Arial','',12)

        # محتوى الجدول
        for i, rec in enumerate(recs):
            x = margin

            # تعديل البيانات - إضافة القاعة بدلاً من القسم
            data = [rec.get('القاعة',''), rec.get('تاريخ_الازدياد',''), rec.get('الرمز',''), rec.get('الاسم_الكامل',''), rec.get('رقم_الامتحان',''), str(i+1)]

            # رسم خلايا الصف
            for j, cell in enumerate(data):
                pdf.set_xy(x, y)
                pdf.cell(cols3[j], ROW_HEIGHT_TABLE2, pdf.ar_text(cell), border=1, align='C')
                x += cols3[j]

            # تحديث الموقع الرأسي للصف التالي
            y += ROW_HEIGHT_TABLE2

            # الانتقال إلى صفحة جديدة عند الحاجة مع إضافة الشعار
            if y > pdf.h - 20:
                pdf.add_page()
                y = pdf.get_y()
                # إضافة الشعار في الصفحة الجديدة
                if logo_path:
                    x_logo = (pdf.w - LOGO_W) / 2
                    pdf.image(logo_path, x=x_logo, y=y, w=LOGO_W, h=LOGO_H)
                y += LOGO_H + 5
                
                # إضافة رأس الجدول في الصفحة الجديدة
                pdf.set_font('Arial','B',12)
                pdf.set_fill_color(200,200,200)
                
                x = margin
                for k, header in enumerate(TABLE3_HEADERS):
                    pdf.set_xy(x, y)
                    pdf.cell(cols3[k], ROW_HEIGHT_TABLE_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
                    x += cols3[k]
                
                y += ROW_HEIGHT_TABLE_HEADER
                pdf.set_font('Arial','',12)

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    pdf.output(output_path)
    print(f"تم إنشاء التقرير: {output_path}")

def generate_absence_report(records, absence_data, output_path, logo_path=None):
    """إنشاء تقرير الغياب"""
    pdf = ArabicPDF()
    margin = 10
    
    pdf.add_page()
    y = pdf.get_y()
    
    # إضافة الشعار
    if logo_path:
        x_logo = (pdf.w - LOGO_W) / 2
        pdf.image(logo_path, x=x_logo, y=y, w=LOGO_W, h=LOGO_H)
    y += LOGO_H + 5

    # العنوان الرئيسي
    pdf.set_font('Arial', 'B', 16)
    pdf.set_text_color(0, 0, 128)
    title_text = f"تقرير الغياب - {absence_data['subject']}"
    pdf.set_xy(margin, y)
    pdf.cell(pdf.w - 2*margin, 15, pdf.ar_text(title_text), border=1, align='C')
    y += 20

    # معلومات التقرير
    pdf.set_font('Arial', 'B', 12)
    pdf.set_text_color(0, 0, 0)
    
    info_data = [
        ['التاريخ:', absence_data['date']],
        ['الفترة:', absence_data['period']],
        ['المادة:', absence_data['subject']],
        ['عدد الغائبين:', str(len(records))],
        ['ملاحظات:', absence_data.get('notes', '')]
    ]
    
    for label, value in info_data:
        pdf.set_xy(margin, y)
        pdf.cell(50, 8, pdf.ar_text(label), border=1, align='C')
        pdf.cell(pdf.w - 2*margin - 50, 8, pdf.ar_text(value), border=1, align='R')
        y += 8
    
    y += 10
    
    # جدول الغائبين - بدون تجميع حسب القاعة
    if records:
        # ترتيب السجلات حسب رقم الامتحان
        sorted_records = sorted(records, key=lambda x: int(x.get('رقم_الامتحان', '0')))
        
        # عناوين الجدول
        headers = ['ر.ت', 'رقم الامتحان', 'الاسم الكامل', 'القسم', 'القاعة']
        col_widths = [20, 40, 80, 50, 30]
        
        pdf.set_font('Arial', 'B', 12)
        pdf.set_fill_color(200, 200, 200)
        
        x = margin
        for i, header in enumerate(headers):
            pdf.set_xy(x, y)
            pdf.cell(col_widths[i], 10, pdf.ar_text(header), border=1, align='C', fill=True)
            x += col_widths[i]
        
        y += 10
        pdf.set_font('Arial', '', 10)
        
        # بيانات الغائبين - جميع السجلات في جدول واحد
        for idx, record in enumerate(sorted_records):
            x = margin
            row_data = [
                str(idx + 1),
                record.get('رقم_الامتحان', ''),
                record.get('الاسم_الكامل', ''),
                record.get('القسم', ''),
                record.get('القاعة', '')
            ]
            
            for i, cell_data in enumerate(row_data):
                pdf.set_xy(x, y)
                pdf.cell(col_widths[i], 8, pdf.ar_text(cell_data), border=1, align='C')
                x += col_widths[i]
            
            y += 8
            
            # الانتقال إلى صفحة جديدة عند الحاجة
            if y > pdf.h - 30:
                pdf.add_page()
                y = pdf.get_y()
        
        # إضافة ملاحظات الغياب في النهاية
        if absence_data.get('notes'):
            y += 10
            pdf.set_font('Arial','B',10)
            pdf.set_xy(margin, y)
            pdf.cell(0, 8, pdf.ar_text(f"ملاحظات: {absence_data['notes']}"), border=1, align='R')
            y += 10

        # إضافة مساحة للتوقيع في النهاية
        y += 20
        signature_width = (pdf.w - 2*margin) / 2
        pdf.set_font('Arial','B',12)
        
        # إضافة مساحة للخاتم والتوقيع
        x = margin + (pdf.w - 2*margin) / 4  # وضع في المنتصف
        
        # مربع الخاتم والتوقيع
        pdf.set_xy(x, y)
        pdf.cell(signature_width, 20, '', border=1, align='C')
        
        # النص أسفل المربع
        pdf.set_xy(x, y + 22)
        pdf.cell(signature_width, 8, pdf.ar_text('خاتم وتوقيع السيد(ة) رئيس(ة) المركز'), border=0, align='C')
    
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    pdf.output(output_path)
    return True

def print_exams_report(parent=None, level=None, report_title=None, subject_data=None):
    """
    دالة لإنشاء محضر توقيعات المترشحين، يمكن استدعاؤها من واجهات PyQt5

    المعاملات:
        parent: كائن النافذة الأم (لعرض رسائل)
        level: المستوى لتصفية البيانات (اختياري)
        report_title: عنوان المحضر (اختياري)
        subject_data: بيانات المادة (اختياري)

    العوائد:
        (success, output_path, message): ثلاثية تحدد نجاح العملية ومسار الملف ورسالة النتيجة
    """
    try:
        # تحديد مسار قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'data.db')

        # تخصيص استعلام SQL حسب المستوى إذا تم تحديده
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # استخدام عنوان التقرير المخصص إذا تم تمريره

        # استعلام مخصص إذا تم تحديد المستوى
        if level:
            query = '''
            SELECT رقم_الامتحان, الاسم_الكامل, الرمز, مركز_الامتحان, المستوى, القسم, القاعة, تاريخ_الازدياد
            FROM امتحانات
            WHERE المستوى = ?
            ORDER BY CAST(رقم_الامتحان AS INTEGER), CAST(القاعة AS INTEGER)
            '''
            cursor.execute(query, (level,))
        else:
            query = '''
            SELECT رقم_الامتحان, الاسم_الكامل, الرمز, مركز_الامتحان, المستوى, القسم, القاعة, تاريخ_الازدياد
            FROM امتحانات
            ORDER BY CAST(رقم_الامتحان AS INTEGER), CAST(القاعة AS INTEGER)
            '''
            cursor.execute(query)

        cols = [c[0] for c in cursor.description]
        records = [dict(zip(cols, row)) for row in cursor.fetchall()]

        # جلب شعار المؤسسة
        cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
        logo_row = cursor.fetchone()
        logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None

        conn.close()

        # التحقق من وجود سجلات
        if not records:
            return False, None, "لم يتم العثور على سجلات مطابقة."

        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الامتحانات')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # تحديد اسم الملف بناءً على المستوى
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        level_suffix = f"_{level}" if level else ""
        output_path = os.path.join(reports_dir, f"تقرير_الحضور{level_suffix}_{timestamp}.pdf")

        # إنشاء التقرير مع تمرير عنوان التقرير وبيانات المادة
        generate_report(logo_path, records, output_path, report_title, subject_data)

        # فتح الملف بعد إنشائه
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', output_path])
            else:  # Linux
                subprocess.call(['xdg-open', output_path])
        except Exception as e:
            return True, output_path, f"تم إنشاء التقرير ولكن تعذر فتح الملف: {str(e)}"

        return True, output_path, "تم إنشاء التقرير بنجاح."
    except Exception as e:
        traceback.print_exc()
        return False, None, f"حدث خطأ: {str(e)}"

def print_absence_report(parent=None, selected_records=None, absence_data=None):
    """دالة لإنشاء تقرير الغياب"""
    try:
        if not selected_records or not absence_data:
            return False, None, "لا توجد بيانات كافية لإنشاء التقرير."

        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير الامتحانات')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # تحديد اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        subject_name = absence_data.get('subject', 'غير_محدد').replace(' ', '_')
        output_path = os.path.join(reports_dir, f"تقرير_الغياب_{subject_name}_{timestamp}.pdf")

        # جلب شعار المؤسسة
        try:
            db_path = os.path.join(os.path.dirname(__file__), 'data.db')
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            logo_row = cursor.fetchone()
            logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None
            conn.close()
        except:
            logo_path = None

        # إنشاء التقرير
        success = generate_absence_report(selected_records, absence_data, output_path, logo_path)
        
        if success:
            # فتح الملف بعد إنشائه
            try:
                if sys.platform == 'win32':
                    os.startfile(output_path)
                elif sys.platform == 'darwin':  # macOS
                    subprocess.call(['open', output_path])
                else:  # Linux
                    subprocess.call(['xdg-open', output_path])
            except Exception as e:
                return True, output_path, f"تم إنشاء التقرير ولكن تعذر فتح الملف: {str(e)}"

            return True, output_path, "تم إنشاء تقرير الغياب بنجاح."
        else:
            return False, None, "فشل في إنشاء تقرير الغياب."
            
    except Exception as e:
        traceback.print_exc()
        return False, None, f"حدث خطأ: {str(e)}"

if __name__=='__main__':
    try:
        db = os.path.join(os.path.dirname(__file__), 'data.db')
        logo, recs = fetch_records(db)
        out = os.path.join(os.path.expanduser('~'),'Desktop','تقارير الامتحانات', f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf")
        generate_report(logo, recs, out)
    except Exception as e:
        print(f"خطأ: {e}")
        traceback.print_exc()
